SECRET_KEY = '%vj35+iv2v-v3-m-t)%hfe=7&^ftvlfwsjwe+hqjy%44gggp%y'
HTTP_STATIC_TOKEN = 'deviareLg9VwCn6J1lVkPxcAYJqnd-academyADbybpNvp34JSrmnYatHyUme'
 
# Redis Configuration
REDIS_HOST = '127.0.0.1'
 
# Database Configuration
# DB_NAME = 'deviare-lp-newstage'
# DB_NAME = 'deviare-lp-newprod'
# DB_USER = 'deviareroot'
# DB_PASSWORD = '7b3368d1-5449-4629-a682-9d013db82c2a'
# DB_HOST = 'deviaredb-prod.cz1yml32kck7.af-south-1.rds.amazonaws.com'
# DB_PORT = '3306'


# DB_NAME = 'deviare-lp-newprod'
DB_NAME = 'deviare-lp-newstage'

DB_USER = 'deviareroot'
DB_PASSWORD = 'mzR3BunWKXHHYDwWV0fm'
# DB_PASSWORD = '7b3368d1-5449-4629-a682-9d013db82c2a'
DB_HOST = 'deviaredb-prod-new.cz1yml32kck7.af-south-1.rds.amazonaws.com'
# DB_HOST = 'deviaredb-prod.cz1yml32kck7.af-south-1.rds.amazonaws.com'
DB_PORT = '3306'
 
# Email Configuration
# EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'outlook.office365.com'
# EMAIL_HOST = 'smtp.office365.com'
EMAIL_HOST_USER = '<EMAIL>'
# EMAIL_HOST_PASSWORD = 'Lam82139'
EMAIL_HOST_PASSWORD = 'V%598555425711us'
 
# Client Configuration
CLIENT_ID = '99b5c7f62d6e38ac340f36e3accea1ea'
CLIENT_SECRET = '********************************'
AUTHORIZATION_BASE_URL = 'https://accounts.fundanathi.live/oauth2/auth'
TOKEN_URL = 'https://accounts.fundanathi.live/oauth2/token'
 
# Keycloak Configuration
# KEYCLOAK_URL = 'https://identity.deviare.africa/auth/'
KEYCLOAK_URL = 'https://identity-staging.deviare.africa/auth/'
 
# KEYCLOAK_DOMAIN = 'identity.deviare.africa'
KEYCLOAK_DOMAIN = 'identity-staging.deviare.africa'
# KEYCLOAK_ISSUER = 'identity.deviare.africa'
KEYCLOAK_ISSUER = 'identity-staging.deviare.africa'

KEYCLOAK_REALM = 'Deviare'
# KEYCLOAK_CLIENTID = 'prodBackend'#'stagingBackend'#'api-backend'
KEYCLOAK_CLIENTID = 'stagingBackend'#'api-backend'
# KEYCLOAK_CLIENTSECRET = 'e5ac1be4-2a29-43d3-958a-820ad58169fe'
KEYCLOAK_CLIENTSECRET = '2893202e-7709-4615-8c63-26af92bce2fc'
#KEYCLOAK_ADMINUSER = '<EMAIL>'
KEYCLOAK_ADMINUSER = 'realmadmin'#'<EMAIL>'
#KEYCLOAK_ADMINPASSWORD = 'ashu8090'
KEYCLOAK_ADMINPASSWORD = 'longisland'
 
# Proxy Configuration
PROXY_URL = 'http://deviare-elb-staging-**********.af-south-1.elb.amazonaws.com'
PROXY_CONFIG_PATH = '/etc/apache2/FormFiller/example'
PROXY_APACHE_PATH = '/etc/apache2/sites-enabled/000-default.conf'
 
# Other Configurations
URL = 'http://platform-staging.deviare.africa'
 
# AWS S3 Configuration
AWS_S3_ACCESS_KEY = '********************'
AWS_S3_SECRET_KEY = '7Yjx27B6tGKy0SIKnoH9kudbyIC1IivS/RIovpvb'
AWS_S3_BUCKET_NAME = 'elearn-stat'
 
# Deviare Config Client Configuration
CONFIG_CLIENT_ID = '86f1964d-f9eb-4c16-818f-2cb561e2a268'
CONFIG_CLIENT_SECRET = '********************************'
CONFIG_MS_AUTHORITY = 'https://login.microsoftonline.com'
CONFIG_MS_GRAPH_POINT = 'https://graph.microsoft.com/v1.0{0}'
CONFIG_MS_OAUTH_REDIRECT_URL = 'https://api-staging.deviare.africa/read_outlook_mail'
 
# AWS CloudFront Configuration
CLOUD_FRONT_DISTRIBUTION_ID = 'E1A8KZCRCBFQQ2'
CLOUD_FRONT_DISTRIBUTION_ID_STRATEGIC = 'E2G2IZV5RMEO3A'
CLOUD_FRONT_DISTRIBUTION_NAME_STRATEGIC = 'drvoiodvlgrqk.cloudfront.ne'
CLOUD_FRONT_DISTRIBUTION_NAME = 'd2gv56lwfaj1dz.cloudfront.net'
ROUTE53_LMS_WHITELABELING_INSTANCE = 'whitelabelling-1783690226.us-east-1.elb.amazonaws.com'
ROUTE53_XPERTSKILLS_WHITELABELING_INSTANCE = 'lmsx-whitelabelling-2006010236.us-east-1.elb.amazonaws.com'
 
# Badge DB Instance Configuration
BADGE_DB_NAME = 'badgr'
BADGE_DB_USER = 'deviareroot'
BADGE_DB_PASSWORD = 'mzR3BunWKXHHYDwWV0fm'
BADGE_DB_HOST = 'deviaredb-prod-new.cz1yml32kck7.af-south-1.rds.amazonaws.com'
BADGE_DB_PORT = '3306'
 
# Practice Labs Server Configuration
PRACTICE_LABS_SERVER_URL = 'https://www.practice-labs.com/api/v1/authentication/login'
PRACTICE_LABS_USERNAME = '<EMAIL>'
PRACTICE_LABS_PASSWORD = 'BKqGTo0GHUv8Psw'
PRACTICE_LABS_DOMAIN = 'DEV01'
 
# Sales and Operation Team Emails
SALES_EMAILS = '<EMAIL>'
 
#TALENT LMS configuration
 
TLMS_API_KEY = 'Eqe74jwlzCmhYwJDV2mVuiV6ApPieh'
TLMS_URL_ADDRESS = 'https://learning.staging.deviare.co.za'

# ASSESSMENT CONFIGURATION
# DTMM_SURVEY_ID = '845952'
# SURVEY_URL = 'https://readiness.deviare.africa/index.php'
SURVEY_URL = 'https://readiness-staging.deviare.africa/limesurvey/index.php'
DTMM_SURVEY_ID = '316386'
LIME_SURVEY_URL = 'https://readiness-staging.deviare.africa/limesurvey/index.php/admin/remotecontrol'

# for prod
# PROD_CLIENT_ID='6W1I82srzyle21KA3ISYt7zha'
# PROD_CLIENT_SECRET='QB0coCnuEYtVcwq5rr0iecp4fjHhpPdNQd70F5hYpIdPFae00a'
# PROD_X_API_KEY='Dk9fQDLKkW76lSn4YjTdH1m7FtgLwh0D3Oh2qXSG'
# PROD_EXTERNAL_API_URL='https://api.raven360.com'

# for stage
PROD_CLIENT_ID='aVcR6V2287nwJWO1KOiRNsCic'
PROD_CLIENT_SECRET='nA52H6NoSllOmjJ394uri70XycaZRaK7BHIaXHJFcAAjwkcnMa'
PROD_X_API_KEY='m3W3ixKtfe121dD4Y5G6V7C9q528KtAW6InhWvKN'
PROD_EXTERNAL_API_URL='https://api.sandbox.raven360.com'

PROD_SURVEY_URL = 'https://readiness.deviare.africa/index.php'
AIMM_SURVEY_ID = '129299'

https://readiness.deviare.africa/index.php
https://readiness-staging.deviare.africa/limesurvey/index.php/admin/remotecontrol


# SENDGRID_API_KEY='*********************************************************************'

SENDGRID_API_KEY='*********************************************************************'

