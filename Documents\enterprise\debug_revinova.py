#!/usr/bin/env python
"""
Debug script to check Revinova course data and enrollment
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import Course, CourseLicenseUser, UserSettings

def debug_revinova_courses():
    print("=== DEBUGGING REVINOVA COURSES ===")

    # Get the specific UUIDs from the API response
    revinova_uuids = [
        "15b3a07f-fb49-456d-9e5f-2780bc043848",
        "9ce1c999-2811-4251-a10f-6ab0cde163de"
    ]

    # Check for common test users
    test_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]

    print(f"\nLooking for courses with UUIDs: {revinova_uuids}")
    print(f"Checking enrollment for test emails: {test_emails}")

    for uuid in revinova_uuids:
        print(f"\n--- Course UUID: {uuid} ---")
        try:
            course = Course.objects.get(uuid=uuid)
            print(f"Name: '{course.name}'")
            print(f"Provider: '{course.provider}'")
            print(f"Link: '{course.link}'")
            print(f"Course ID: '{course.course_id}'")
            print(f"Course Type: '{course.course_type}'")

            # Check enrollments
            enrollments = CourseLicenseUser.objects.filter(
                course_license_id__course_id=course
            )
            print(f"Total enrollments: {enrollments.count()}")

            if enrollments.exists():
                print("Enrolled users:")
                for enrollment in enrollments[:5]:  # Show first 5
                    print(f"  - {enrollment.user_id.email}")

                # Check if any test users are enrolled
                for email in test_emails:
                    try:
                        user = UserSettings.objects.get(email=email)
                        is_enrolled = enrollments.filter(user_id=user).exists()
                        print(f"  Test user {email}: {'ENROLLED' if is_enrolled else 'NOT ENROLLED'}")
                    except UserSettings.DoesNotExist:
                        print(f"  Test user {email}: USER NOT FOUND")

        except Course.DoesNotExist:
            print(f"Course with UUID {uuid} not found!")
        except Exception as e:
            print(f"Error: {e}")
    
    # Also check all Revinova courses
    print(f"\n--- ALL REVINOVA COURSES ---")
    revinova_courses = Course.objects.filter(provider__icontains="revinova")
    print(f"Total Revinova courses: {revinova_courses.count()}")
    
    for course in revinova_courses[:10]:  # Show first 10
        print(f"UUID: {course.uuid}, Name: '{course.name}', Link: '{course.link[:50]}...' if course.link else 'None'")

if __name__ == "__main__":
    debug_revinova_courses()
