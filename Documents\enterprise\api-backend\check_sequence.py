#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import <PERSON>, ComposerCourse, ComposerAssessment, ComposerApprenticeship

# Check composer 1
try:
    c = Composer.objects.get(id=1)
    print(f'Composer: {c.product_name}')
    print(f'is_sequence: {c.is_sequence}')
    print()
    
    print('Through table data:')
    print('Courses:')
    for cc in ComposerCourse.objects.filter(composer=c).order_by('sequence_number'):
        print(f'  {cc.course.name} - seq: {cc.sequence_number}')
    
    print('Assessments:')
    for ca in ComposerAssessment.objects.filter(composer=c).order_by('sequence_number'):
        print(f'  {ca.assessment.name} - seq: {ca.sequence_number}')
    
    print('Apprenticeships:')
    for cap in ComposerApprenticeship.objects.filter(composer=c).order_by('sequence_number'):
        print(f'  {cap.apprenticeship.name} - seq: {cap.sequence_number}')
        
except Exception as e:
    print(f'Error: {e}')
