#!/usr/bin/env python3

import requests
import json

def test_assign_customer_license_get():
    """Test the AssignCustomerLicense GET method with composer support"""
    
    # API endpoint
    url = "http://127.0.0.1:8000/main/assign_customer_license"
    
    # Test customer ID from user's cURL command
    customer_id = "19accb1e-ff97-4677-95ad-a7922814c537"
    
    # Headers
    headers = {
        'Authorization': 'Token 0f64649910738022d4b003fe73d81d57407aef18',
        'Content-Type': 'application/json'
    }
    
    # Parameters
    params = {
        'customer_id': customer_id
    }
    
    print(f"Testing AssignCustomerLicense GET method...")
    print(f"URL: {url}")
    print(f"Customer ID: {customer_id}")
    print(f"Headers: {headers}")
    print(f"Params: {params}")
    print("-" * 50)
    
    try:
        # Make the GET request
        response = requests.get(url, headers=headers, params=params, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Response JSON:")
                print(json.dumps(data, indent=2))
                
                # Check if composer data is included
                if 'data' in data and isinstance(data['data'], dict):
                    response_data = data['data']
                    
                    print("\n" + "="*50)
                    print("ANALYSIS OF RESPONSE DATA:")
                    print("="*50)
                    
                    # Check for composer in allocated resources
                    if 'composer' in response_data:
                        print(f"✅ COMPOSER DATA FOUND in allocated resources:")
                        print(f"   Composers: {len(response_data['composer'])} items")
                        for composer in response_data['composer']:
                            print(f"   - ID: {composer.get('id')}, Name: {composer.get('name')}, Count: {composer.get('count')}, Total: {composer.get('total_count')}")
                    else:
                        print("❌ COMPOSER DATA MISSING from allocated resources")
                    
                    # Check for composer in dropdown data
                    if 'other_data' in response_data and 'composer' in response_data['other_data']:
                        print(f"✅ COMPOSER DROPDOWN DATA FOUND:")
                        print(f"   Available Composers: {len(response_data['other_data']['composer'])} items")
                        for composer in response_data['other_data']['composer'][:3]:  # Show first 3
                            print(f"   - ID: {composer.get('id')}, Name: {composer.get('product_name')}")
                    else:
                        print("❌ COMPOSER DROPDOWN DATA MISSING")
                    
                    # Check for composer counts
                    if 'remaining_composer' in response_data:
                        print(f"✅ COMPOSER COUNTS FOUND:")
                        print(f"   Remaining Composers: {response_data.get('remaining_composer')}")
                        print(f"   Total Composers: {response_data.get('total_composer')}")
                    else:
                        print("❌ COMPOSER COUNTS MISSING")
                    
                    # Show other resource types for comparison
                    print(f"\nOTHER RESOURCE TYPES:")
                    for resource_type in ['course', 'assessment', 'apprenticeship', 'lab']:
                        if resource_type in response_data:
                            print(f"   {resource_type.title()}: {len(response_data[resource_type])} items")
                
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
                print(f"Raw response: {response.text}")
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out after 30 seconds")
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    test_assign_customer_license_get()
