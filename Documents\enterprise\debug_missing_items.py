#!/usr/bin/env python
"""
Debug why some courses and assessments are missing from the API response
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import Course, Assessment, CourseLicenseUser, AssessmentLicenseUser, UserSettings, Composer

def debug_missing_items():
    print("=== DEBUGGING MISSING ITEMS ===")
    
    # Get the test user
    user = UserSettings.objects.get(email="<EMAIL>")
    print(f"User: {user.email}")
    
    # Get the composer
    composer = Composer.objects.get(id=1)
    print(f"Composer: {composer.product_name}")
    
    print("\n=== COURSES ===")
    all_courses = composer.courses.all()
    print(f"Total courses in composer: {all_courses.count()}")
    
    for course in all_courses:
        print(f"\nCourse: {course.name} ({course.provider})")
        print(f"UUID: {course.uuid}")
        
        # Check enrollment manually
        enrollment = CourseLicenseUser.objects.filter(
            user_id=user,
            course_license_id__course_id=course
        )
        enrolled = enrollment.exists()
        print(f"Enrolled: {enrolled}")
        
        # Check with my filtering logic
        filtered = composer.courses.filter(
            course_licenses__users__user_id=user,
            uuid=course.uuid
        ).exists()
        print(f"Passes filter: {filtered}")
        
        if enrolled != filtered:
            print("❌ MISMATCH! Manual check and filter give different results")
    
    print("\n=== ASSESSMENTS ===")
    all_assessments = composer.assessments.all()
    print(f"Total assessments in composer: {all_assessments.count()}")
    
    for assessment in all_assessments:
        print(f"\nAssessment: {assessment.name}")
        print(f"UUID: {assessment.uuid}")
        
        # Check assignment manually
        assignment = AssessmentLicenseUser.objects.filter(
            user_id=user,
            assessment_license_id__assessment_id=assessment
        )
        assigned = assignment.exists()
        print(f"Assigned: {assigned}")
        
        # Check with my filtering logic
        filtered = composer.assessments.filter(
            assessment_licenses__users__user_id=user,
            uuid=assessment.uuid
        ).exists()
        print(f"Passes filter: {filtered}")
        
        if assigned != filtered:
            print("❌ MISMATCH! Manual check and filter give different results")

if __name__ == "__main__":
    debug_missing_items()
