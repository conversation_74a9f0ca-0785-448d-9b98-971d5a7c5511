#!/usr/bin/env python
"""
Check which courses the test user is enrolled in
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import Course, CourseLicenseUser, UserSettings, CourseLicense

def check_user_enrollments():
    print("=== CHECKING USER ENROLLMENTS ===")
    
    # Get the test user
    test_email = "<EMAIL>"
    try:
        user = UserSettings.objects.get(email=test_email)
        print(f"User: {user.email} (UUID: {user.uuid})")
        
        # Get all enrollments for this user
        enrollments = CourseLicenseUser.objects.filter(user_id=user)
        print(f"Total enrollments: {enrollments.count()}")
        
        print("\nEnrolled courses:")
        for enrollment in enrollments:
            course = enrollment.course_license_id.course_id
            print(f"- {course.name} ({course.provider}) - UUID: {course.uuid}")
            print(f"  Course License ID: {enrollment.course_license_id.uuid}")
            print(f"  Launch URL in CourseLicenseUser: {enrollment.launch_url}")
            print(f"  Link in Course: {course.link}")
            print()
        
        # Check specifically for the Revinova courses from the API response
        revinova_uuids = [
            "15b3a07f-fb49-456d-9e5f-2780bc043848",
            "9ce1c999-2811-4251-a10f-6ab0cde163de"
        ]
        
        print("=== CHECKING SPECIFIC REVINOVA COURSES ===")
        for uuid in revinova_uuids:
            try:
                course = Course.objects.get(uuid=uuid)
                enrollment = CourseLicenseUser.objects.filter(
                    user_id=user,
                    course_license_id__course_id=course
                )
                print(f"Course: {course.name} (UUID: {uuid})")
                print(f"Provider: {course.provider}")
                print(f"Link: {course.link}")
                print(f"User enrolled: {enrollment.exists()}")
                if enrollment.exists():
                    print(f"Enrollment record: {enrollment.first().uuid}")
                print()
            except Course.DoesNotExist:
                print(f"Course {uuid} not found")
                
    except UserSettings.DoesNotExist:
        print(f"User {test_email} not found")

if __name__ == "__main__":
    check_user_enrollments()
