#!/usr/bin/env python
"""
Add the enrolled Revinova course to the composer
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import Course, Composer

def add_revinova_to_composer():
    print("=== ADDING REVINOVA COURSE TO COMPOSER ===")
    
    # Get the enrolled Revinova course
    enrolled_revinova_uuid = "00284ba3-26e8-4a2a-9d2a-5d1991c4554e"
    course = Course.objects.get(uuid=enrolled_revinova_uuid)
    
    # Get the "My Bundle" composer
    composer = Composer.objects.get(id=1)
    
    print(f"Adding course: {course.name}")
    print(f"To composer: {composer.product_name}")
    
    # Add the course to the composer
    composer.courses.add(course)
    
    print("✅ Course added successfully!")
    
    # Verify it was added
    courses_in_composer = composer.courses.all()
    print(f"\nCourses now in '{composer.product_name}':")
    for c in courses_in_composer:
        print(f"  - {c.name} ({c.provider}) - UUID: {c.uuid}")

if __name__ == "__main__":
    add_revinova_to_composer()
