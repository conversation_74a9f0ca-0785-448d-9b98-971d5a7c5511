0000000000000000000000000000000000000000 6d2c0149f439e10f7164679833ed12ef3cc9b1a3 <PERSON><PERSON><PERSON> <<EMAIL>> 1745513133 +0530	clone: from bitbucket.org:deviare/api-backend.git
6d2c0149f439e10f7164679833ed12ef3cc9b1a3 6fa676cd784ff2ecfca7ee037d32eddb082dcb43 <PERSON><PERSON><PERSON> <<EMAIL>> 1745514269 +0530	checkout: moving from master to staging
6fa676cd784ff2ecfca7ee037d32eddb082dcb43 6fa676cd784ff2ecfca7ee037d32eddb082dcb43 <PERSON><PERSON><PERSON> <<EMAIL>> 1745557303 +0530	checkout: moving from staging to updatetaskname
6fa676cd784ff2ecfca7ee037d32eddb082dcb43 c0bbe128a4f5b7388b881010a0a17477e78e3fe6 <PERSON><PERSON><PERSON> <<EMAIL>> 1745557325 +0530	commit: update taskname
c0bbe128a4f5b7388b881010a0a17477e78e3fe6 c0bbe128a4f5b7388b881010a0a17477e78e3fe6 Ashutosh Kumar <<EMAIL>> 1745671348 +0530	checkout: moving from updatetaskname to savesurveyresponsedb
c0bbe128a4f5b7388b881010a0a17477e78e3fe6 191d17fd120835c5f2221f190eb8962c74b7ff0a Ashutosh Kumar <<EMAIL>> 1745671402 +0530	commit: saving lime survey response in db.
191d17fd120835c5f2221f190eb8962c74b7ff0a 191d17fd120835c5f2221f190eb8962c74b7ff0a Ashutosh Kumar <<EMAIL>> 1745770743 +0530	checkout: moving from savesurveyresponsedb to updattaskfordbupdate
191d17fd120835c5f2221f190eb8962c74b7ff0a cbf349ddb1ae157d98842ce270204a934f384e97 Ashutosh Kumar <<EMAIL>> 1745770783 +0530	commit: update limesurvey & user report talnetlms
cbf349ddb1ae157d98842ce270204a934f384e97 cbf349ddb1ae157d98842ce270204a934f384e97 Ashutosh Kumar <<EMAIL>> 1745823131 +0530	reset: moving to HEAD
cbf349ddb1ae157d98842ce270204a934f384e97 cbf349ddb1ae157d98842ce270204a934f384e97 Ashutosh Kumar <<EMAIL>> 1745832924 +0530	reset: moving to HEAD
cbf349ddb1ae157d98842ce270204a934f384e97 cbf349ddb1ae157d98842ce270204a934f384e97 Ashutosh Kumar <<EMAIL>> 1745846002 +0530	checkout: moving from updattaskfordbupdate to updateCustomerAdminDashboard
cbf349ddb1ae157d98842ce270204a934f384e97 20b151a3785439ccd5ef0ffbfb842342d61ff14a Ashutosh Kumar <<EMAIL>> 1745846048 +0530	commit: update customer admin dashboard
20b151a3785439ccd5ef0ffbfb842342d61ff14a d35a1e650594fddacd03eeee187fdce7eee3caec Ashutosh Kumar <<EMAIL>> 1745858942 +0530	pull origin staging: Fast-forward
d35a1e650594fddacd03eeee187fdce7eee3caec d35a1e650594fddacd03eeee187fdce7eee3caec Ashutosh Kumar <<EMAIL>> 1745998173 +0530	checkout: moving from updateCustomerAdminDashboard to createApiforlimesurvey
d35a1e650594fddacd03eeee187fdce7eee3caec 8fd300a90a8e07dba7b6af812009573e318c506e Ashutosh Kumar <<EMAIL>> 1745998300 +0530	commit: create API for lime Survey
8fd300a90a8e07dba7b6af812009573e318c506e 48748b7f8409dfc66def119eec620b09d8cf0652 Ashutosh Kumar <<EMAIL>> 1746167276 +0530	pull origin staging: Fast-forward
48748b7f8409dfc66def119eec620b09d8cf0652 48748b7f8409dfc66def119eec620b09d8cf0652 Ashutosh Kumar <<EMAIL>> 1746700716 +0530	reset: moving to HEAD
48748b7f8409dfc66def119eec620b09d8cf0652 b36fde625acbb478b4966cf04fd8cd79aed0d77c Ashutosh Kumar <<EMAIL>> 1746700735 +0530	pull origin staging: Fast-forward
b36fde625acbb478b4966cf04fd8cd79aed0d77c cb1d14c1afbf5aac9b9a34457e0930cbf822e10f Ashutosh Kumar <<EMAIL>> 1746710098 +0530	commit: update survey name
cb1d14c1afbf5aac9b9a34457e0930cbf822e10f c20f63e285be87aba2c96291c7981d20c4c7da78 Ashutosh Kumar <<EMAIL>> 1746711472 +0530	commit: update survey names for assessment
c20f63e285be87aba2c96291c7981d20c4c7da78 c20f63e285be87aba2c96291c7981d20c4c7da78 Ashutosh Kumar <<EMAIL>> 1747029493 +0530	reset: moving to HEAD
c20f63e285be87aba2c96291c7981d20c4c7da78 1ad0c673f3040ee9b7ec01c5dc8f9df62a2ee43c Ashutosh Kumar <<EMAIL>> 1747120286 +0530	commit: update lime survey response
1ad0c673f3040ee9b7ec01c5dc8f9df62a2ee43c 42a351f5e8fab0c63c679cd19675d5c40abf197b Ashutosh Kumar <<EMAIL>> 1747124716 +0530	pull origin staging: Fast-forward
42a351f5e8fab0c63c679cd19675d5c40abf197b 42a351f5e8fab0c63c679cd19675d5c40abf197b Ashutosh Kumar <<EMAIL>> 1747131035 +0530	checkout: moving from createApiforlimesurvey to aiassesment
42a351f5e8fab0c63c679cd19675d5c40abf197b bbfa9bbe299ce9ba5a412ff32ea95d5b8c6f6f00 Ashutosh Kumar <<EMAIL>> 1747131056 +0530	commit: add ai assesment
bbfa9bbe299ce9ba5a412ff32ea95d5b8c6f6f00 0140c332b0b83777290bd7b2e87bde870ff5060e Ashutosh Kumar <<EMAIL>> 1747133756 +0530	commit: update aimw name
0140c332b0b83777290bd7b2e87bde870ff5060e 40ade320882ba7ca8b0a2c3ff14ffe027e65f9fb Ashutosh Kumar <<EMAIL>> 1747204516 +0530	commit: add pagination in UserSurveyAveragesView
40ade320882ba7ca8b0a2c3ff14ffe027e65f9fb 40ade320882ba7ca8b0a2c3ff14ffe027e65f9fb Ashutosh Kumar <<EMAIL>> 1747227971 +0530	checkout: moving from aiassesment to csmadmincount
40ade320882ba7ca8b0a2c3ff14ffe027e65f9fb 451e3537624417299454c594ceac34735af72064 Ashutosh Kumar <<EMAIL>> 1747228000 +0530	commit: update csmadmincountissue
451e3537624417299454c594ceac34735af72064 90a8f8f3d9998db7c65dd227cb9b81c1a57b9cf6 Ashutosh Kumar <<EMAIL>> 1747301985 +0530	commit: update usersurveyrsponse & add download-user-survey-averages
90a8f8f3d9998db7c65dd227cb9b81c1a57b9cf6 90a8f8f3d9998db7c65dd227cb9b81c1a57b9cf6 Ashutosh Kumar <<EMAIL>> 1747641304 +0530	reset: moving to HEAD
90a8f8f3d9998db7c65dd227cb9b81c1a57b9cf6 943f260ccbc9ddc7c3dfa31bb164de1c5366d597 Ashutosh Kumar <<EMAIL>> 1747650834 +0530	commit: update in API for lime survey report
943f260ccbc9ddc7c3dfa31bb164de1c5366d597 d006a59b80e29e026a33bb3790ddeebfd8b087ea Ashutosh Kumar <<EMAIL>> 1747653144 +0530	pull origin staging: Fast-forward
d006a59b80e29e026a33bb3790ddeebfd8b087ea 42a351f5e8fab0c63c679cd19675d5c40abf197b Ashutosh Kumar <<EMAIL>> 1747653158 +0530	checkout: moving from csmadmincount to createApiforlimesurvey
42a351f5e8fab0c63c679cd19675d5c40abf197b d006a59b80e29e026a33bb3790ddeebfd8b087ea Ashutosh Kumar <<EMAIL>> 1747653175 +0530	pull origin staging: Fast-forward
d006a59b80e29e026a33bb3790ddeebfd8b087ea 6fa676cd784ff2ecfca7ee037d32eddb082dcb43 Ashutosh Kumar <<EMAIL>> 1747653195 +0530	checkout: moving from createApiforlimesurvey to staging
6fa676cd784ff2ecfca7ee037d32eddb082dcb43 d006a59b80e29e026a33bb3790ddeebfd8b087ea Ashutosh Kumar <<EMAIL>> 1747653210 +0530	pull origin staging: Fast-forward
d006a59b80e29e026a33bb3790ddeebfd8b087ea d006a59b80e29e026a33bb3790ddeebfd8b087ea Ashutosh Kumar <<EMAIL>> 1747725165 +0530	checkout: moving from staging to assesmentname
d006a59b80e29e026a33bb3790ddeebfd8b087ea 6350c059581d3bc2f938616c5074d6735ba7a080 Ashutosh Kumar <<EMAIL>> 1747725208 +0530	commit: assesment name allocated dynamically
6350c059581d3bc2f938616c5074d6735ba7a080 6350c059581d3bc2f938616c5074d6735ba7a080 Ashutosh Kumar <<EMAIL>> 1747734486 +0530	reset: moving to HEAD
6350c059581d3bc2f938616c5074d6735ba7a080 c5bb3a2d5453bd078485e27b0ccd12cd97368c10 Ashutosh Kumar <<EMAIL>> 1747738236 +0530	commit: filtering bug fixed & reports download
c5bb3a2d5453bd078485e27b0ccd12cd97368c10 d006a59b80e29e026a33bb3790ddeebfd8b087ea Ashutosh Kumar <<EMAIL>> 1747739786 +0530	checkout: moving from assesmentname to staging
d006a59b80e29e026a33bb3790ddeebfd8b087ea 1e1d76ea335d5190e7f4653637f583e6c5ff669f Ashutosh Kumar <<EMAIL>> 1747739808 +0530	pull origin staging: Fast-forward
1e1d76ea335d5190e7f4653637f583e6c5ff669f 1e1d76ea335d5190e7f4653637f583e6c5ff669f Ashutosh Kumar <<EMAIL>> 1747828231 +0530	checkout: moving from staging to fixassessment
1e1d76ea335d5190e7f4653637f583e6c5ff669f 7d7e87771579ea142e05aeaf4f37be286eeada1f Ashutosh Kumar <<EMAIL>> 1747828268 +0530	commit: fix insight count for every role
7d7e87771579ea142e05aeaf4f37be286eeada1f ac9822865802481502f89452750ad9c6295ba5a0 Ashutosh Kumar <<EMAIL>> 1747829826 +0530	pull origin staging: Fast-forward
ac9822865802481502f89452750ad9c6295ba5a0 ac9822865802481502f89452750ad9c6295ba5a0 Ashutosh Kumar <<EMAIL>> 1748241320 +0530	checkout: moving from fixassessment to talentaccess
ac9822865802481502f89452750ad9c6295ba5a0 146166d96b7551fac7aaed978570ddc283718b68 Ashutosh Kumar <<EMAIL>> 1748241505 +0530	commit: added talent_access columns in company table
146166d96b7551fac7aaed978570ddc283718b68 23f2265ee7df000c6e38215507323c7496d9a2f5 Ashutosh Kumar <<EMAIL>> 1748321681 +0530	pull origin staging: Fast-forward
23f2265ee7df000c6e38215507323c7496d9a2f5 be4b934aa221ed42c330d7ba7bd76479a269dc85 Ashutosh Kumar <<EMAIL>> 1748321730 +0530	commit: added send API to users who completed the talelelms courses
be4b934aa221ed42c330d7ba7bd76479a269dc85 ea613fe6bc146b52fd50e0b225b95c24b5953848 Ashutosh Kumar <<EMAIL>> 1748359200 +0530	pull origin staging: Fast-forward
ea613fe6bc146b52fd50e0b225b95c24b5953848 ea613fe6bc146b52fd50e0b225b95c24b5953848 Ashutosh Kumar <<EMAIL>> 1748608132 +0530	checkout: moving from talentaccess to revinovaupdatecode
ea613fe6bc146b52fd50e0b225b95c24b5953848 445a0505468f14e4be0f6840aeef9f0de9e15a50 Ashutosh Kumar <<EMAIL>> 1748608369 +0530	commit: revinova celery task code has been optimized
445a0505468f14e4be0f6840aeef9f0de9e15a50 445a0505468f14e4be0f6840aeef9f0de9e15a50 Ashutosh Kumar <<EMAIL>> 1748867031 +0530	reset: moving to HEAD
445a0505468f14e4be0f6840aeef9f0de9e15a50 2f08c684ddcec80147b7c168237051fa5690cb5a Ashutosh Kumar <<EMAIL>> 1748867795 +0530	commit: company all user list which has assigned talentlms course
2f08c684ddcec80147b7c168237051fa5690cb5a d8d78c20433c0217f880b7c8846d3d6727d418c3 Ashutosh Kumar <<EMAIL>> 1748922388 +0530	pull origin staging: Fast-forward
d8d78c20433c0217f880b7c8846d3d6727d418c3 1e1d76ea335d5190e7f4653637f583e6c5ff669f Ashutosh Kumar <<EMAIL>> 1748922511 +0530	checkout: moving from revinovaupdatecode to staging
1e1d76ea335d5190e7f4653637f583e6c5ff669f d8d78c20433c0217f880b7c8846d3d6727d418c3 Ashutosh Kumar <<EMAIL>> 1748922525 +0530	pull origin staging: Fast-forward
d8d78c20433c0217f880b7c8846d3d6727d418c3 d8d78c20433c0217f880b7c8846d3d6727d418c3 Ashutosh Kumar <<EMAIL>> 1748945186 +0530	checkout: moving from staging to Revinovaupdatecode
d8d78c20433c0217f880b7c8846d3d6727d418c3 9092a2326e269891907db41e116f31af740b512c Ashutosh Kumar <<EMAIL>> 1748945259 +0530	commit: add revinova course in both CompanyTalentUsers & CompanyAllUsers Api
9092a2326e269891907db41e116f31af740b512c 2a48e989aeb251d3edfc0899bea3222d757f3c63 Ashutosh Kumar <<EMAIL>> 1748946592 +0530	pull origin staging: Fast-forward
2a48e989aeb251d3edfc0899bea3222d757f3c63 4c0ac0899388d8ead995727ce97a02d46e6f10be Ashutosh Kumar <<EMAIL>> 1748952072 +0530	commit: optimize revinova celery task code
4c0ac0899388d8ead995727ce97a02d46e6f10be be8bb624c6e44a250dd8de3e8090927ae26047f8 Ashutosh Kumar <<EMAIL>> 1748953597 +0530	pull origin staging: Fast-forward
be8bb624c6e44a250dd8de3e8090927ae26047f8 d8d78c20433c0217f880b7c8846d3d6727d418c3 Ashutosh Kumar <<EMAIL>> 1748953608 +0530	checkout: moving from Revinovaupdatecode to staging
d8d78c20433c0217f880b7c8846d3d6727d418c3 be8bb624c6e44a250dd8de3e8090927ae26047f8 Ashutosh Kumar <<EMAIL>> 1748953628 +0530	pull origin staging: Fast-forward
be8bb624c6e44a250dd8de3e8090927ae26047f8 be8bb624c6e44a250dd8de3e8090927ae26047f8 Ashutosh Kumar <<EMAIL>> 1749019097 +0530	checkout: moving from staging to Revinovaupdatecode
be8bb624c6e44a250dd8de3e8090927ae26047f8 c05006eefe4fe3cdf76738c923af99a62fa02af0 Ashutosh Kumar <<EMAIL>> 1749019129 +0530	commit: update revinova celery task code
c05006eefe4fe3cdf76738c923af99a62fa02af0 319807ded4a339cf8d9e20a5c18263ecff809c2c Ashutosh Kumar <<EMAIL>> 1749020230 +0530	pull origin staging: Fast-forward
319807ded4a339cf8d9e20a5c18263ecff809c2c be8bb624c6e44a250dd8de3e8090927ae26047f8 Ashutosh Kumar <<EMAIL>> 1749035492 +0530	checkout: moving from Revinovaupdatecode to staging
be8bb624c6e44a250dd8de3e8090927ae26047f8 319807ded4a339cf8d9e20a5c18263ecff809c2c Ashutosh Kumar <<EMAIL>> 1749035509 +0530	pull origin staging: Fast-forward
319807ded4a339cf8d9e20a5c18263ecff809c2c 319807ded4a339cf8d9e20a5c18263ecff809c2c Ashutosh Kumar <<EMAIL>> 1749041275 +0530	reset: moving to HEAD
319807ded4a339cf8d9e20a5c18263ecff809c2c 319807ded4a339cf8d9e20a5c18263ecff809c2c Ashutosh Kumar <<EMAIL>> 1749041287 +0530	checkout: moving from staging to Revinovaupdatecode
319807ded4a339cf8d9e20a5c18263ecff809c2c 5ab2d2dbb86040b5ed7dee37520cbe45743b7e1f Ashutosh Kumar <<EMAIL>> 1749041450 +0530	commit: revert revinova celery task code
5ab2d2dbb86040b5ed7dee37520cbe45743b7e1f 5ab2d2dbb86040b5ed7dee37520cbe45743b7e1f Ashutosh Kumar <<EMAIL>> 1749098424 +0530	reset: moving to HEAD
5ab2d2dbb86040b5ed7dee37520cbe45743b7e1f b968faea8175fde66296d65b0e062781d2d46c27 Ashutosh Kumar <<EMAIL>> 1749098442 +0530	pull origin staging: Fast-forward
b968faea8175fde66296d65b0e062781d2d46c27 319807ded4a339cf8d9e20a5c18263ecff809c2c Ashutosh Kumar <<EMAIL>> 1749098484 +0530	checkout: moving from Revinovaupdatecode to staging
319807ded4a339cf8d9e20a5c18263ecff809c2c b968faea8175fde66296d65b0e062781d2d46c27 Ashutosh Kumar <<EMAIL>> 1749098505 +0530	pull origin staging: Fast-forward
b968faea8175fde66296d65b0e062781d2d46c27 b968faea8175fde66296d65b0e062781d2d46c27 Ashutosh Kumar <<EMAIL>> 1749098540 +0530	checkout: moving from staging to Revinovaupdatecode
b968faea8175fde66296d65b0e062781d2d46c27 7764c432929ca67cecfeb895f00f424901bb1cfd Ashutosh Kumar <<EMAIL>> 1749100583 +0530	commit: updated revinova celery task code for coursecompletion
7764c432929ca67cecfeb895f00f424901bb1cfd b968faea8175fde66296d65b0e062781d2d46c27 Ashutosh Kumar <<EMAIL>> 1749101794 +0530	checkout: moving from Revinovaupdatecode to staging
b968faea8175fde66296d65b0e062781d2d46c27 d136ae37c439eb7eab13686819997abedfbe85c6 Ashutosh Kumar <<EMAIL>> 1749101809 +0530	pull origin staging: Fast-forward
d136ae37c439eb7eab13686819997abedfbe85c6 d136ae37c439eb7eab13686819997abedfbe85c6 Ashutosh Kumar <<EMAIL>> 1749235787 +0530	checkout: moving from staging to aiassesmentreport
d136ae37c439eb7eab13686819997abedfbe85c6 a3b6ce519c267afab6eb887513c48037adfbda5d Ashutosh Kumar <<EMAIL>> 1749235818 +0530	commit: add celery code for save ai assessment report
a3b6ce519c267afab6eb887513c48037adfbda5d d136ae37c439eb7eab13686819997abedfbe85c6 Ashutosh Kumar <<EMAIL>> 1749355690 +0530	checkout: moving from aiassesmentreport to staging
d136ae37c439eb7eab13686819997abedfbe85c6 91ea30667e435eb3028abcc434fcf572c71e0d20 Ashutosh Kumar <<EMAIL>> 1749355785 +0530	pull origin staging: Fast-forward
91ea30667e435eb3028abcc434fcf572c71e0d20 91ea30667e435eb3028abcc434fcf572c71e0d20 Ashutosh Kumar <<EMAIL>> 1749360324 +0530	checkout: moving from staging to updatetalentcode
91ea30667e435eb3028abcc434fcf572c71e0d20 163d4c310c00d560300f5c84565fadb2676555bf Ashutosh Kumar <<EMAIL>> 1749360369 +0530	commit: update celery talent report code
163d4c310c00d560300f5c84565fadb2676555bf 91ea30667e435eb3028abcc434fcf572c71e0d20 Ashutosh Kumar <<EMAIL>> 1749532251 +0530	checkout: moving from updatetalentcode to staging
91ea30667e435eb3028abcc434fcf572c71e0d20 1e5909e487fd55651b066a6a938205b34df8fb40 Ashutosh Kumar <<EMAIL>> 1749532295 +0530	pull origin staging: Fast-forward
1e5909e487fd55651b066a6a938205b34df8fb40 1e5909e487fd55651b066a6a938205b34df8fb40 Ashutosh Kumar <<EMAIL>> 1749537021 +0530	checkout: moving from staging to celerytime
1e5909e487fd55651b066a6a938205b34df8fb40 8f4095a424bd2e8558ac54dda2907e3bc7b49908 Ashutosh Kumar <<EMAIL>> 1749537060 +0530	commit: update celery time
8f4095a424bd2e8558ac54dda2907e3bc7b49908 17d21c02478b9739a13bb38b0e63b186e7f79da4 Ashutosh Kumar <<EMAIL>> 1749546762 +0530	pull origin staging: Fast-forward
17d21c02478b9739a13bb38b0e63b186e7f79da4 d444abc636e08a15fbc3e4f38d194e776111439c Ashutosh Kumar <<EMAIL>> 1749618525 +0530	commit: update enterprise user launch talent course
d444abc636e08a15fbc3e4f38d194e776111439c 1cbb1dd5af93f3ec6648f4546ecd57bf0a66cd0e Ashutosh Kumar <<EMAIL>> 1749624799 +0530	commit: update celery time in docker
1cbb1dd5af93f3ec6648f4546ecd57bf0a66cd0e 04f2ed6ac1b6e1c7e42aaeca94a804378c70b054 Ashutosh Kumar <<EMAIL>> 1749644085 +0530	commit: revert register user talentlms code
04f2ed6ac1b6e1c7e42aaeca94a804378c70b054 1e5909e487fd55651b066a6a938205b34df8fb40 Ashutosh Kumar <<EMAIL>> 1749795262 +0530	checkout: moving from celerytime to staging
1e5909e487fd55651b066a6a938205b34df8fb40 e105cf5a0a706b57cdab3e2827dced421766f7d3 Ashutosh Kumar <<EMAIL>> 1749795280 +0530	pull origin staging: Fast-forward
e105cf5a0a706b57cdab3e2827dced421766f7d3 e105cf5a0a706b57cdab3e2827dced421766f7d3 Ashutosh Kumar <<EMAIL>> 1750058650 +0530	checkout: moving from staging to badgefix
e105cf5a0a706b57cdab3e2827dced421766f7d3 40a50953e9f0e3349980865db45b48949af744fd Ashutosh Kumar <<EMAIL>> 1750058702 +0530	commit: badge fix.....
40a50953e9f0e3349980865db45b48949af744fd 40a50953e9f0e3349980865db45b48949af744fd Ashutosh Kumar <<EMAIL>> 1750095548 +0530	checkout: moving from badgefix to manualCeleryTrigger
40a50953e9f0e3349980865db45b48949af744fd 4f227d229175aa81dcc25e89f037dbf4a2579dff Ashutosh Kumar <<EMAIL>> 1750095732 +0530	commit: manually trigger task
4f227d229175aa81dcc25e89f037dbf4a2579dff f9916ccc768105d2a2ffa43f5a9913a41e7bcccb Ashutosh Kumar <<EMAIL>> 1750144665 +0530	commit: update manually trigger task
f9916ccc768105d2a2ffa43f5a9913a41e7bcccb 4398f00971681959b60846b4fe71e3931ef07c65 Ashutosh Kumar <<EMAIL>> 1750147166 +0530	pull origin staging: Fast-forward
4398f00971681959b60846b4fe71e3931ef07c65 e105cf5a0a706b57cdab3e2827dced421766f7d3 Ashutosh Kumar <<EMAIL>> 1750147179 +0530	checkout: moving from manualCeleryTrigger to staging
e105cf5a0a706b57cdab3e2827dced421766f7d3 4398f00971681959b60846b4fe71e3931ef07c65 Ashutosh Kumar <<EMAIL>> 1750147196 +0530	pull origin staging: Fast-forward
4398f00971681959b60846b4fe71e3931ef07c65 4398f00971681959b60846b4fe71e3931ef07c65 Ashutosh Kumar <<EMAIL>> 1750155483 +0530	checkout: moving from staging to manualCeleryTrigger
4398f00971681959b60846b4fe71e3931ef07c65 51554d8c55e57026fb53092c8e9b60b5b2024e52 Ashutosh Kumar <<EMAIL>> 1750155548 +0530	commit: update clear the db of celerytask
51554d8c55e57026fb53092c8e9b60b5b2024e52 07b16b36f57055f93bb7186b92b091b629a8866a Ashutosh Kumar <<EMAIL>> 1750157641 +0530	commit: update in docker file
07b16b36f57055f93bb7186b92b091b629a8866a 1549a600daff3bd493416f0ccdf3b371c6d82a30 Ashutosh Kumar <<EMAIL>> 1750221359 +0530	commit: fix cleanup_old_task_results task
1549a600daff3bd493416f0ccdf3b371c6d82a30 2fa2bcc427aee75f0d1838eefce211a2fd187374 Ashutosh Kumar <<EMAIL>> 1750672290 +0530	commit: manual triggered tasks fix
2fa2bcc427aee75f0d1838eefce211a2fd187374 2fa2bcc427aee75f0d1838eefce211a2fd187374 Ashutosh Kumar <<EMAIL>> 1750685690 +0530	reset: moving to HEAD
2fa2bcc427aee75f0d1838eefce211a2fd187374 280c5d56489388d36531669dce200af4e8ccb8df Ashutosh Kumar <<EMAIL>> 1750740023 +0530	pull origin staging: Fast-forward
280c5d56489388d36531669dce200af4e8ccb8df 280c5d56489388d36531669dce200af4e8ccb8df Ashutosh Kumar <<EMAIL>> 1750751546 +0530	checkout: moving from manualCeleryTrigger to asAssessmentResponse
280c5d56489388d36531669dce200af4e8ccb8df 864de70a72ccf7bf284ed16c53c63d89881bdb2c Ashutosh Kumar <<EMAIL>> 1750751585 +0530	commit: Ai assessment report api completed
864de70a72ccf7bf284ed16c53c63d89881bdb2c 9c8c0d7be3d0133e9b3474f30b72f544c2cd5e48 Ashutosh Kumar <<EMAIL>> 1750761730 +0530	commit: Ai assessment report api completed..
9c8c0d7be3d0133e9b3474f30b72f544c2cd5e48 9c8c0d7be3d0133e9b3474f30b72f544c2cd5e48 Ashutosh Kumar <<EMAIL>> 1751447871 +0530	checkout: moving from asAssessmentResponse to composer
9c8c0d7be3d0133e9b3474f30b72f544c2cd5e48 2f46e531b73be0a75312c3c864ecc529b32e561a Ashutosh Kumar <<EMAIL>> ********** +0530	commit: initial composer requirements done.
2f46e531b73be0a75312c3c864ecc529b32e561a e671e37a7fcdd713a4c854c20d75c47baceb1f3f Ashutosh Kumar <<EMAIL>> ********** +0530	commit: add user can launh the assigned composer.
e671e37a7fcdd713a4c854c20d75c47baceb1f3f 3b26f14137670f4b8453d40b741f5ab194a81b40 Ashutosh Kumar <<EMAIL>> ********** +0530	commit: added the composer license to customer.
3b26f14137670f4b8453d40b741f5ab194a81b40 4dc8fdda5b2c58ea4191dcbedbc7478617195fe8 Ashutosh Kumar <<EMAIL>> ********** +0530	commit: added the composer license to project
4dc8fdda5b2c58ea4191dcbedbc7478617195fe8 4dc8fdda5b2c58ea4191dcbedbc7478617195fe8 Ashutosh Kumar <<EMAIL>> ********** +0530	reset: moving to HEAD
4dc8fdda5b2c58ea4191dcbedbc7478617195fe8 3a409cb0452d1b3c5e8caabb9d962ecac7cb96ec Ashutosh Kumar <<EMAIL>> ********** +0530	pull origin staging: Fast-forward
