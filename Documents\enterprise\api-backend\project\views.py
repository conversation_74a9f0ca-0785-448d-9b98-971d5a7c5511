from django.http import JsonResponse
from django.core.paginator import Paginator, EmptyPage

from django.db.models.functions import Cast
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, generics, filters
import numpy as np
import os
import json
import base64
import requests
import pandas as pd

from django.core import mail
from django.core.mail.backends.smtp import EmailBackend
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.core.mail import EmailMessage

from random import randint
import datetime
from django.core import serializers

from tools.decorators import data_response
from deviare import settings as deviare_settings
from main.models import (
    UserSettings,
    Company,
    Project,
    Course,
    CourseLicense,
    CourseLicenseUser,

    Assessment,
    AssessmentLicense,
    AssessmentLicenseUser,
    GCIndexAssessment,
    Apprenticeship,
    TMForumUserAssessment,
    ApprenticeshipLicense,
    ApprenticeshipLicenseUser, DeploymentLicense,
    AssignLicenseToCustomer, LabLicense, LabLicenseUser, Lab, LabCourseLinkedLicense,ElearninStates

)
from main.serializers import (
    CompanySerializer,
    UserSettingsSerializer,
    CourseSerializer,
    GCIndexAssessmentSerializer,
    ProjectSerializer,
    CourseLicenseSerializer,
    CourseLicenseUserSerializer,
    LabSerializer
)
from project.serializers import (
    ProjectSummarySerializer,
    CourseLicenseDetailSerializer,
    AssessmentLicenseDetailSerializer,
    ApprenticeshipLicenseSerializer,
    AssessmentLicenseDashboardSerializer,
    ProjectViewSerializer,
    ApprenticeshipLicenseUserDisplaySerializer, LabLicenseSerializer
)

from main.views import responsedata, get_impersonate_token
from django.shortcuts import get_object_or_404
from rest_framework.exceptions import ValidationError
import logging
from django.db import transaction
from django.db.models import (Count, Sum, F, Case, When, Q, Value as V, TextField, IntegerField)
from main.tasks import assign_user_to_course_on_talent_lms, add_courseTobranch

logger = logging.getLogger(__name__)

API_KEY = deviare_settings.TLMS_API_KEY
URL_ADDRESS = f'{deviare_settings.TLMS_URL_ADDRESS}/api/v1'


def description(desc):
    """used in reg courses to remove special characters"""

    spe_chars = ['?', 'â', '€™', '€', 'Æ']
    new_desc = ''

    for item in desc:
        if item not in spe_chars:
            new_desc += item

    return new_desc


def send_email(recipient, subject, body, msg_html=None):
    try:
        FROM = "<EMAIL>"
        # email_queue(recipient, subject, body, msg_html)
        TO = recipient if type(recipient) is list else [recipient]
        msg = send_mail(subject, body, FROM, TO, html_message=msg_html, fail_silently=False)
    except Exception as e:
        logger.exception(e)
        pass


class CreateProject(APIView):
    """Logged-in super admin can create a project"""

    def post(self, request):
        from pprint import pprint
        # Auth check
        if request.auth is None:
            return Response(
                responsedata(False, "You are Unauthorized"),
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            # Check if project exists
            r_body = request.data
            if "project_name" in r_body and r_body["project_name"] == "":
                return Response(
                    responsedata(False, "Fill in Project Name"), status=status.HTTP_400_BAD_REQUEST
                )
            elif "company_id" in r_body and r_body["company_id"] == None or r_body["company_id"] == "None":
                return Response(
                    responsedata(False, "Select Company  Name"), status=status.HTTP_400_BAD_REQUEST
                )
            elif "company_admin_id" in r_body and r_body["company_admin_id"] == None or r_body[
                "company_admin_id"] == "None":
                return Response(
                    responsedata(False, "Select Company Admin"), status=status.HTTP_400_BAD_REQUEST
                )

            print('fdadsadas')
            pprint(request.data, indent=2)
            if Project.objects.filter(
                    project_name=request.data.get("project_name")
            ).exists():
                return Response(
                    responsedata(False, "Project already exists"),
                    status=status.HTTP_200_OK,
                )
            with transaction.atomic():
                """
                Project does not exist. Create Project
                along with its dependencies, i.e CourseLicense and Assessment License.
                Return 200 response on succesful creation.
                """
                # Company, Admin, created by check
                if request.data.get("company_id") and request.data.get("company_admin_id"):
                    customer = request.data.get("company_id")
                    customer_admin = dict(uuid=request.data.get("company_admin_id"))
                    user = list(
                        UserSettings.objects.filter(userName=request.user.username).values(
                            "uuid"
                        )
                    )[0]
                else:
                    return Response(
                        responsedata(False, "Missing some details"),
                        status=status.HTTP_200_OK,
                    )

                # Creating Project
                data = {
                    "company_id": customer,
                    "project_admin": request.data.get('project_admin'),
                    "company_admin_id": request.data.get('company_admin'),
                    "superadmin_id": user["uuid"],
                    "description": request.data.get('description'),
                    "project_name": request.data.get('project_name')
                }
                serializer = ProjectSerializer(data=data)
                serializer.is_valid(raise_exception=True)
                serializer.save()

                project_data = {
                    "uuid": serializer.data.get("uuid"),
                    "company": serializer.data.get("company_id"),
                }
                project = project_data

                # Assigning Courses
                for course in request.data.get("course", []):
                    if len(course.get('course_id', '')) > 1:
                        # Check if course already exists
                        if CourseLicense.objects.filter(
                                project_id=project["uuid"], course_id=course["course_id"]
                        ).exists():
                            continue
                        # Saving Course License
                        data = {
                            "course_id": course["course_id"],
                            "count": course["count"],
                            "project_id": project["uuid"],
                            "initial_count": course["count"],

                        }
                        cs = AssignLicenseToCustomer.objects.filter(customer_id=request.data.get("company_id")).first()
                        course_count = cs.course_customer.filter(course_customer_for__uuid=course["course_id"]).first()
                        if int(course_count.count) < int(course["count"]):
                            return Response(responsedata(True, "You don't have availabe license", serializer.data),
                                            status=status.HTTP_400_BAD_REQUEST,
                                            )
                        else:
                            course_count.count = str(int(course_count.count) - int(course["count"]))
                            course_count.save()
                            serializer = CourseLicenseSerializer(data=data)
                            serializer.is_valid(raise_exception=True)
                            serializer.save()
                # Assigning Assessments
                for assessment in request.data.get("assessment", []):
                    if len(assessment.get('assessment_id', '')) > 1:
                        if 'uuid' in project:
                            count = assessment.get('count', 1)
                            data = {
                                "assessment_id_id": assessment["assessment_id"],
                                "project_id_id": project["uuid"],
                            }
                            data_count = {
                                "count": count if str(count).isdigit() else 1,
                                "initial_count": count if str(count).isdigit() else 1,
                            }
                            qs = AssessmentLicense.objects.filter(**data)
                            cs = AssignLicenseToCustomer.objects.filter(
                                customer_id=request.data.get("company_id")).first()
                            assesment_count = cs.assessment_customer.filter(
                                assessment_customer_for__uuid=assessment["assessment_id"]).first()
                            if int(assesment_count.count) < int(count):
                                return Response(responsedata(True, "You don't have availabe license", serializer.data),
                                                status=status.HTTP_400_BAD_REQUEST,
                                                )
                            else:
                                assesment_count.count = str(int(assesment_count.count) - int(count))
                                assesment_count.save()
                            if qs.exists():
                                a = qs.first()
                                a.count = count
                                a.save()
                            else:
                                data.update(data_count)
                                ast = AssessmentLicense.objects.create(**data)
                                ast.save()
                for apprenticeship in request.data.get("apprenticeship", []):
                    if len(apprenticeship.get('apprenticeship_id', '')) > 1:
                        if 'uuid' in project:
                            count = apprenticeship.get('count', 1)
                            data = {
                                "apprenticeship_id": apprenticeship["apprenticeship_id"],
                                "project_id": project["uuid"],
                            }
                            data_count = {
                                "count": count if str(count).isdigit() else 1,
                                "initial_count": count if str(count).isdigit() else 1,
                            }
                            qs = ApprenticeshipLicense.objects.filter(**data)
                            cs = AssignLicenseToCustomer.objects.filter(
                                customer_id=request.data.get("company_id")).first()
                            apprenticeship_count = cs.apprenticeship_customer.filter(
                                apprenticeship_customer_for=apprenticeship["apprenticeship_id"]).first()
                            if int(apprenticeship_count.count) < int(count):
                                return Response(responsedata(True, "You don't have availabe license", serializer.data),
                                                status=status.HTTP_400_BAD_REQUEST,
                                                )
                            else:
                                apprenticeship_count.count = str(int(apprenticeship_count.count) - int(count))
                                apprenticeship_count.save()
                            if qs.exists():
                                a = qs.first()
                                a.count = count
                                a.save()
                            else:
                                data.update(data_count)
                                ap = ApprenticeshipLicense.objects.create(**data)
                                ap.save()
                for lab in request.data.get("lab", []):
                    if len(lab.get('lab_id', '')) > 1:
                        if 'uuid' in project:
                            count = lab.get('count', 1)
                            data = {
                                "lab_id": lab["lab_id"],
                                "project_id": project["uuid"],
                            }
                            data_count = {
                                "count": count if str(count).isdigit() else 1,
                                "initial_count": count if str(count).isdigit() else 1,
                            }
                            qs = LabLicense.objects.filter(**data)
                            cs = AssignLicenseToCustomer.objects.filter(
                                customer_id=request.data.get("company_id")).first()
                            lab_count = cs.lab_customer.filter(
                                lab_customer_for=lab["lab_id"]).first()
                            if int(lab_count.count) < int(count):
                                return Response(responsedata(True, "You don't have available license", serializer.data),
                                                status=status.HTTP_400_BAD_REQUEST,
                                                )
                            else:
                                lab_count.count = str(int(lab_count.count) - int(count))
                                lab_count.save()
                            if qs.exists():
                                a = qs.first()
                                a.count = count
                                a.save()
                            else:
                                data.update(data_count) 
                                data['project_id_id'] = data.pop('project_id')
                                data['lab_id_id'] = data.pop('lab_id')
                                ap = LabLicense.objects.create(**data)
                                ap.save()
            # Everything worked well
            return Response(
                responsedata(True, "Project created successfully", project_data),
                status=status.HTTP_200_OK,
            )
        except Exception as exc:
            logger.exception(exc)
            print(exc,'--')
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LicenseInformation(APIView):
    """To get license information for a project"""

    def post(self, request):

        if request.data.get("project_id"):
            project = request.data.get("project_id")
            courses_info = list(
                CourseLicense.objects.filter(
                    project_id=project
                ).values("course_id", "course_id__name", "count").distinct()
            )
            assessment_info = list(AssessmentLicense.objects.filter(
                project_id=project
            ).values("assessment_id", "assessment_id__name", "count").distinct())
            apprenticeship_info = list(ApprenticeshipLicense.objects.filter(
                project_id=project
            ).values("apprenticeship_id", "apprenticeship_id__name", "count").distinct())

            lab_info = list(LabLicense.objects.filter(
                project_id=project
            ).values("lab_id", "lab_id__name", "count").distinct())

            # New Edited
            deployment_info = list(DeploymentLicense.objects.filter(
                project_id=project
            ).values("deployment_id_id", "deployment_id__name", "count"))
            # New Edited

            # Composer License Information
            from main.models import ComposerLicense
            composer_info = list(ComposerLicense.objects.filter(
                project_id=project
            ).values("composer_id", "composer_id__product_name", "count", "initial_count").distinct())

            data = {
                "courses": courses_info,
                'assessments': assessment_info,
                'apprenticeships': apprenticeship_info,
                'deployments': deployment_info,  # Edited
                'labs': lab_info,  # Edited
                'composers': composer_info  # New: Composer licenses
            }

            return Response(
                responsedata(True, "License Info fetched", data),
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                responsedata(False, "Project ID missing"),
                status=status.HTTP_400_BAD_REQUEST,
            )


class CustomerUsers(APIView):
    """To get list of users belonging to a company"""

    @data_response
    def post(self, request, *args, **kwargs):
        values_args = ["uuid", "customer_users", 'role', 'sub_role']
        filter_kw = False
        annot_kw = {'customer_users': F("userName")}
        if request.data.get("company") and request.data.get("company") != 'null':
            filter_kw = dict(customers__in=[request.data.get("company")])
        else:
            print(kwargs)
            if request.userAuth.role in ['companyadmin', 'projectadmin', 'gcologist']:
                filter_kw = dict(customers__in=request.userAuth.customers.values_list('customers'))
            # if request.userAuth.role == 'cadmin':
            #     filter_kw = dict(customers__in=request.userAuth.customers.values_list('customers'))
        if filter_kw is not False:
            customer_users = list(UserSettings.objects.filter(
                **filter_kw
            ).annotate(**annot_kw).values(
                *values_args
            ).order_by("customer_users"))

            return Response(
                responsedata(True, "Customer users fetched", customer_users),
                status=status.HTTP_200_OK,
            )
        elif request.userAuth.role == 'superadmin':
            customer_users = list(UserSettings.objects.all(

            ).annotate(**annot_kw).values(
                *values_args
            ).order_by("customer_users").exclude(role='superadmin'))

            return Response(
                responsedata(True, "Customer users fetched", customer_users),
                status=status.HTTP_200_OK,
            )

        else:
            return Response(
                responsedata(False, "Company ID missing"),
                status=status.HTTP_400_BAD_REQUEST,
            )


def clean_uuid(uuid='', **kwargs):
    return str(uuid).replace('-', '')


def safe_int(x):
    return int(x) if x is not None and type(x) not in (pd.NaT, np.NAN, np.NaN, np.Inf, bool,) else 0


class UserAllocation(APIView):
    """To allocate user as per license"""

    def send_course_allocation_mail(self, user, course_name):
        from main.views import send_email
        # msg_text = f"Dear {user.firstName},<br><br>"
        # msg_text += f"Your purchase of {course_name} in https://learn.deviareacademy.africa has been successfully processed.<br>"
        # msg_text += f"You can now access your Course Contents at below URL using the same credentials.<br>"
        # msg_text += f"https://platform-dev.deviare.co.za.<br>"
        # msg_text += f"Reach our Support (<EMAIL>) for any further assistance.<br><br>"
        # msg_text += f"Thanks & Regards<br>Team Deviare<br>"

        # mail = send_email(
        #     [user.email, "<EMAIL>", "<EMAIL>", "<EMAIL>"],
        #     "New course allocation",
        #     msg_text,
        #     msg_text,
        # )
        msg_text = f"Dear Admin,<br><br>"
        msg_text += f"{user.firstName} has purchases of {course_name} in https://learn.deviareacademy.africa has been successfully allocated the required License.<br>"
        msg_text += f"You can manage the access to Course Contents at below URL.<br>"
        msg_text += f"https://platform-dev.deviare.co.za.<br>"
        msg_text += f"Thanks & Regards<br>Team Deviare<br>"

        mail = send_email(
            ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
            "New course allocation",
            msg_text,
            msg_text,
        )
        return True

    def put(self, request, pk):
        try:
            Project.objects.get(uuid=pk)
        except:
            return Response(
                responsedata(False, "Invalid Project ID"),
                status=status.HTTP_400_BAD_REQUEST,
            )
        # Course UnAllocation
        if request.data.get("courses", False):
            for course in request.data.get("courses"):
                try:

                    course_license_id = list(
                        CourseLicense.objects.filter(
                            project_id=pk, course_id=course["course_id"]
                        ).values("uuid", "count")
                    )[0]
                    data = {
                        "course_license_id": course_license_id["uuid"],
                        "user_id_id__in": [user['uuid'] for user in course["user"]],
                    }
                    CourseLicenseUser.objects.filter(**data).delete()
                except IndexError:
                    '''
                    In some cases with manipulated data in db, we are getting index error while. It wont be real case scenario.
                    '''
                    pass

        if request.data.get("assessments", False):
            for assessment in request.data.get("assessments"):
                assessment_license_id = list(
                    AssessmentLicense.objects.filter(
                        project_id=pk, assessment_id=assessment["assessment_id"]
                    ).values("uuid", "count")
                )
                if (len(assessment_license_id) > 0):
                    data = {
                        "assessment_license_id": assessment_license_id[0]["uuid"],
                        "user_id_id__in": [user['uuid'] for user in assessment["user"]],
                    }
                    AssessmentLicenseUser.objects.filter(**data).delete()
        if request.data.get("apprenticeships", False):
            for course in request.data.get("apprenticeships"):
                try:
                    course_license_id = list(
                        ApprenticeshipLicense.objects.filter(
                            project_id=pk, apprenticeship_id=course["apprenticeship_id"]
                        ).values("uuid", "count")
                    )[0]
                    data = {
                        "apprenticeship_license_id": course_license_id["uuid"],
                        "user_id__in": [user['uuid'] for user in course["user"]],
                    }
                    ApprenticeshipLicenseUser.objects.filter(**data).delete()
                except IndexError:
                    # In some cases with manipulated data in db, we are getting index error while. It wont be real case scenario.
                    pass
        if request.data.get("labs", False):
            for lab in request.data.get("labs"):
                try:
                    lab_license_id = list(
                        LabLicense.objects.filter(
                            project_id=pk, lab_id=lab["lab_id"]
                        ).values("uuid", "count")
                    )[0]
                    data = {
                        "lab_license_id": lab_license_id["uuid"],
                        "user_id__in": [user['uuid'] for user in lab["user"]],
                    }
                    LabLicenseUser.objects.filter(**data).delete()
                except IndexError:
                    # In some cases with manipulated data in db, we are getting index error while. It wont be real case scenario.
                    pass

        # Composer UnAllocation
        if request.data.get("composers", False):
            from main.models import ComposerLicense, ComposerLicenseUser

            for composer in request.data.get("composers"):
                try:
                    composer_license_id = list(
                        ComposerLicense.objects.filter(
                            project_id=pk, composer_id=composer["composer_id"]
                        ).values("uuid", "count")
                    )[0]
                    data = {
                        "composer_license_id": composer_license_id["uuid"],
                        "user_id__in": [user['uuid'] for user in composer["user"]],
                    }
                    ComposerLicenseUser.objects.filter(**data).delete()
                except IndexError:
                    # In some cases with manipulated data in db, we are getting index error while. It wont be real case scenario.
                    pass

        return Response(
            responsedata(True, "Users Unallocated", ), status=status.HTTP_200_OK
        )

    def post(self, request, pk):

        # Checking Project ID
        cr_id = None
        try:
            Project.objects.get(uuid=pk)
        except Project.DoesNotExist:
            return Response(
                responsedata(False, "Invalid Project ID"),
                status=status.HTTP_400_BAD_REQUEST,
            )
        ret = {

        }
        # Course Allocation
        if request.data.get("courses", False):
            ret['courses'] = {'added': [], 'to_remove': [], 'new_list': []}
            for course in request.data.get("courses"):
                course_license_id = CourseLicense.objects.filter(project_id_id=pk,
                                                                        course_id_id=course["course_id"]).first()
                user_assigned = CourseLicenseUser.objects.filter(course_license_id=course_license_id.uuid).count()
                remaining_license = course_license_id.initial_count - user_assigned
                if len(course["user"]) > remaining_license:
                    return Response(
                        responsedata(True, f"Cannot Assign the licence remaining counts are: {remaining_license}", ), status=status.HTTP_400_BAD_REQUEST
                    )

            for course in request.data.get("courses"):
                try:
                    course_license_id,c = CourseLicense.objects.get_or_create(project_id_id=pk, course_id_id=course["course_id"])
                    # Saving Allocation record
                    current_users = []
                    for user in course["user"]:
                        current_users.append(user["uuid"].replace("-", ""))

                        user = UserSettings.objects.filter(uuid=user["uuid"]).first()

                        # Check if already existing
                        if CourseLicenseUser.objects.filter(course_license_id=course_license_id.uuid, user_id=user).exists():
                            try:
                                cr_id = CourseLicenseUser.objects.get(course_license_id=course_license_id.uuid, user_id=user)
                                if course_license_id.course_id.provider.lower() == 'xpert skills' and not cr_id.launch_url:

                                    url = "https://www.learning.deviareacademy.africa/enrol-user"
                                    payload = json.dumps({
                                        "firstname": user.firstName,
                                        "lastname": user.lastName,
                                        "email": user.email,
                                        "partner_id": 47,
                                        "product_id": int(course_license_id.course_id.course_id_talent_lms)
                                    })
                                    response = requests.post(url, headers={'Content-Type': 'application/json',
                                                                           'Accept': 'application/json'}, data=payload)
                                    if response.status_code in [201, 200]:
                                        content = json.loads(response.text)
                                        cr_id.launch_url = content["access_link"]
                                        cr_id.save()
                            except:
                                pass
                            continue
                        launch_url = None
                        if course_license_id.course_id.provider.lower() == 'xpert skills':
                            url = "https://www.learning.deviareacademy.africa/enrol-user"
                            payload = json.dumps({
                                "firstname": user.firstName,
                                "lastname": user.lastName,
                                "email": user.email,
                                "partner_id": 47,
                                "product_id": int(course_license_id.course_id.course_id_talent_lms)
                            })
                            response = requests.post(url, headers={'Content-Type': 'application/json',
                                                                   'Accept': 'application/json'}, data=payload)
                            if response.status_code in [201, 200] and response.text != '':
                                content = json.loads(response.text)
                                launch_url = content["access_link"]
                        data = {
                            "course_license_id": course_license_id.uuid,
                            "user_id": user.uuid,
                            "launch_url": launch_url
                        }
                        serializer = CourseLicenseUserSerializer(data=data)
                        if serializer.is_valid(raise_exception=True):
                            serializer.save()
                            try:
                                cr_id = CourseLicenseUser.objects.get(uuid=serializer.data['uuid'])
                                if request.data.get("email"):
                                    import threading
                                    # self.send_course_allocation_mail(cr_id.user_id, cr_id.course_license_id.course_id.name)
                                    threading.Thread(target=self.send_course_allocation_mail,
                                                     kwargs={"user": cr_id.user_id,
                                                             "course_name": cr_id.course_license_id.course_id.name}).start()
                            except Exception as e:
                                print(str(e))
                                pass

                    # Deleting deselected users
                    all_users = []
                    all_cl_users = list(CourseLicenseUser.objects.filter(course_license_id=course_license_id.uuid).values("user_id"))
                    for cl_user in all_cl_users:
                        cl_user["user_id"] = str(cl_user["user_id"]).replace("-", "")
                        all_users.append(cl_user["user_id"])
                except IndexError:
                    pass
            try:
                if cr_id:
                    add_courseTobranch(cr_id.course_license_id.course_id.course_id_talent_lms,
                                       cr_id.user_id.customers.all().first().branch_id.lmsbranch_id)
            except:
                pass
            # assign_user_to_course_on_talent_lms.apply_async(kwargs=request.data)
            assign_user_to_course_on_talent_lms(kwargs=request.data)
        if request.data.get('assessments', False):
            for assessment in request.data.get("assessments"):
                assessment_license_id = AssessmentLicense.objects.filter(project_id_id=pk,
                                                                    assessment_id=assessment["assessment_id"]).first()
                user_assigned = AssessmentLicenseUser.objects.filter(assessment_license_id=assessment_license_id.uuid).count()
                remaining_license = assessment_license_id.initial_count - user_assigned
                if len(assessment["user"]) > remaining_license:
                    return Response(
                        responsedata(True, f"Cannot Assign the licence remaining counts are: {remaining_license}", ),
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            for assessment in request.data.get('assessments'):
                try:
                    assessment_license_id = list(
                        AssessmentLicense.objects.filter(
                            project_id=pk, assessment_id=assessment["assessment_id"]
                        ).values("uuid", "count")
                    )[0]
                except:
                    pass

                # Saving Allocation record
                allocate_assessments = []
                filter_kw = {
                    "assessment_license_id_id": assessment_license_id["uuid"],
                }
                qs = AssessmentLicenseUser.objects.filter(**filter_kw)
                existing = list(qs.values_list("user_id", flat=True))
                
                # Track new users for email sending
                new_users = []
                for user in assessment["user"]:
                    if user["uuid"] not in existing:
                        user_id = user["uuid"]
                        data = {
                            "user_id_id": user_id,
                        }
                        if not qs.filter(**data).exists():
                            data.update(filter_kw)
                            allocate_assessments.append(AssessmentLicenseUser(**data))
                            new_users.append(user_id)  # Add to new users list for email

                # Bulk create new allocations
                if allocate_assessments:
                    AssessmentLicenseUser.objects.bulk_create(allocate_assessments)
                    
                    # Send emails only to newly added users
                    for user_id in new_users:
                        domain_name = "Contribute to Your Organization’s Digital Future – Talent Readiness Assessment"
                        from_email = None
                        try:
                            us = UserSettings.objects.get(uuid=user_id)
                            email = us.email
                            email_data = us.customers.all().first()
                            if email_data.partner == "strategy_partner" and (email_data.smtp == True
                                                                            and email_data.smtp_host != None):
                                email_creds = email_data.smtp_host
                                from_email = email_creds.email_host_user
                                domain_name = email_creds.email_subject_prifix
                                URL = "https://" + email_data.domain
                            elif email_data.partner == "reseller_partner":
                                URL = "https://" + email_data.domain
                            elif email_data.partner == "strategy_partner":
                                domain_name = email_data.name + " Login Details"
                                URL = "https://" + email_data.domain
                            else:
                                URL = deviare_settings.URL
                        except:
                            URL = deviare_settings.URL
                            
                        # body1 = "Dear Candidate,\n\n"
                        # body2 = f"You have been invited to take an assessment by Deviare\n\n\n\n"
                        # body3 = "You are champion in Digital Transformation"
                        
                        # Render the HTML template
                        assessment_obj = Assessment.objects.get(uuid=assessment["assessment_id"])
                        html_content = render_to_string('email/assessment_invitation.html', {
                            'first_name': us.firstName if us.firstName else 'Candidate',
                            'assessment_url': f"{URL}/assessments",
                            'assessment_name': assessment_obj.name  # Pass the name dynamically
                        })
                        
                        logger.info('BEFORE EMAIL')
                        if email_data.partner == "strategy_partner" and (
                                email_data.smtp_host != None and email_data.smtp == True):
                            con = mail.get_connection()
                            con.open()
                            mail_obj = EmailBackend(
                                host=email_data.smtp_host.email_host,
                                port=587,
                                password=email_data.smtp_host.email_host_password,
                                username=email_data.smtp_host.email_host_user,
                                use_tls=True,
                                timeout=25)
                            msg = EmailMessage(
                                subject=domain_name,
                                body=html_content,
                                from_email=email_data.smtp_host.email_host_user,
                                to=[email],
                                connection=con)
                            msg.content_subtype = 'html'
                            mail_obj.send_messages([msg])
                            mail_obj.close()
                        else:
                            # Use send_email with html_content for both body and msg_html
                            send_email(
                                recipient=[email],
                                subject=domain_name,
                                body=html_content, 
                                msg_html=html_content
                            )
        if request.data.get('apprenticeships', False):
            for apprenticeship in request.data.get("apprenticeships"):
                apprenticeship_license_id = ApprenticeshipLicense.objects.filter(project=pk,
                                                                        apprenticeship=apprenticeship["apprenticeship_id"]).first()
                user_assigned = ApprenticeshipLicenseUser.objects.filter(apprenticeship_license=apprenticeship_license_id.uuid).count()
                remaining_license = apprenticeship_license_id.initial_count - user_assigned
                if len(apprenticeship["user"]) > remaining_license:
                    return Response(
                        responsedata(True, f"Cannot Assign the licence remaining counts are: {remaining_license}", ), status=status.HTTP_400_BAD_REQUEST
                    )
            for apprenticeship in request.data.get('apprenticeships'):
                apprenticeship_license_id = list(
                    ApprenticeshipLicense.objects.filter(
                        project_id=pk, apprenticeship_id=apprenticeship["apprenticeship_id"]
                    ).values("uuid", "count")
                )[0]

                # Saving Allocation record
                #
                allocate_apprenticeships = []
                filter_kw = {
                    "apprenticeship_license_id": apprenticeship_license_id["uuid"],
                }
                qs = ApprenticeshipLicenseUser.objects.filter(**filter_kw)
                existing = list(qs.values_list("user_id", flat=True))
                for user in apprenticeship["user"]:
                    if user["uuid"] not in existing:
                        user = user["uuid"]
                        data = {
                            "user_id": user,
                        }
                        # Check if already existing
                        if not qs.filter(**data).exists():
                            data.update(filter_kw)
                            allocate_apprenticeships.append(ApprenticeshipLicenseUser(**data))
                # print(allocate_apprenticeships)
                tmp = ApprenticeshipLicenseUser.objects.bulk_create(allocate_apprenticeships)
        if request.data.get('labs', False):
            # for lab in request.data.get("labs"):
            #     lab_license_id = LabLicense.objects.filter(project_id=pk,
            #                                                             lab_id=lab["lab_id"]).first()
            #     user_assigned = LabLicenseUser.objects.filter(lab_license_id=lab_license_id.uuid).count()
            #     remaining_license = lab_license_id.initial_count - user_assigned
            #     if len(lab["user"]) > remaining_license:
            #         return Response(
            #             responsedata(True, f"Cannot Assign the licence remaining counts are: {remaining_license}", ), status=status.HTTP_400_BAD_REQUEST
            #         )
            for lab in request.data.get('labs'):
                lab_license_id = list(LabLicense.objects.filter(
                        project_id=pk, lab_id=lab["lab_id"]
                    ).values("uuid", "count")
                )[0]
                # Saving Allocation record
                allocate_labs = []
                filter_kw = {
                    "lab_license_id_id": lab_license_id["uuid"],
                }
                qs = LabLicenseUser.objects.filter(**filter_kw)
                existing = list(qs.values_list("user_id", flat=True))
                for user in lab["user"]:
                    if user["uuid"] not in existing:
                        user = user["uuid"]
                        data = {
                            "user_id": user,
                        }
                        # Check if already existing
                        if not qs.filter(**data).exists():
                            filter_kw['lab_license_id_id'] = filter_kw.pop('lab_license_id_id')
                            data.update(filter_kw)
                            data['user_id_id'] = data.pop('user_id')
                            allocate_labs.append(LabLicenseUser(**data))
                tmp = LabLicenseUser.objects.bulk_create(allocate_labs)

        # Composer License Allocation
        if request.data.get('composers', False):
            from main.models import ComposerLicense, ComposerLicenseUser
            from main.utils import assign_composer_to_user

            for composer in request.data.get("composers"):
                composer_license_id = ComposerLicense.objects.filter(
                    project_id=pk, composer_id=composer["composer_id"]
                ).first()

                if not composer_license_id:
                    return Response(
                        responsedata(True, f"No composer license found for composer {composer['composer_id']} in this project"),
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Validate remaining licenses
                user_assigned = ComposerLicenseUser.objects.filter(composer_license_id=composer_license_id.uuid).count()
                remaining_license = composer_license_id.initial_count - user_assigned
                if len(composer["user"]) > remaining_license:
                    return Response(
                        responsedata(True, f"Cannot assign composer license. Remaining counts are: {remaining_license}"),
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Assign composer to each user
                for user in composer["user"]:
                    result = assign_composer_to_user(composer["composer_id"], user["uuid"])
                    if not result["success"]:
                        return Response(
                            responsedata(True, result["message"]),
                            status=status.HTTP_400_BAD_REQUEST
                        )

        return Response(
            responsedata(True, "Users Allocated", ), status=status.HTTP_200_OK
        )


class ComposerLicenseAllocation(APIView):
    """
    API endpoint for allocating composer licenses to projects.
    This follows the same pattern as course/assessment/apprenticeship license allocation.
    """

    def post(self, request):
        """
        Allocate composer licenses to a project.
        Expected payload:
        {
            "project_id": "project-uuid",
            "composers": [
                {
                    "composer_id": "composer-uuid",
                    "count": 10
                }
            ]
        }
        """
        try:
            from main.models import Project, Composer, ComposerLicense
            from main.utils import allocate_composer_license_to_project

            project_id = request.data.get("project_id")
            composers = request.data.get("composers", [])

            if not project_id:
                return Response(
                    responsedata(False, "Project ID is required"),
                    status=status.HTTP_400_BAD_REQUEST
                )

            if not composers:
                return Response(
                    responsedata(False, "Composers list is required"),
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate project exists
            try:
                project = Project.objects.get(uuid=project_id)
            except Project.DoesNotExist:
                return Response(
                    responsedata(False, "Project not found"),
                    status=status.HTTP_400_BAD_REQUEST
                )

            results = []
            for composer_data in composers:
                composer_id = composer_data.get("composer_id")
                count = composer_data.get("count", 1)

                if not composer_id:
                    return Response(
                        responsedata(False, "Composer ID is required for each composer"),
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Validate composer exists
                try:
                    composer = Composer.objects.get(id=composer_id)  # Composer uses 'id' not 'uuid'
                except Composer.DoesNotExist:
                    return Response(
                        responsedata(False, f"Composer {composer_id} not found"),
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Validate customer has sufficient licenses (following the existing pattern)
                from main.models import AssignLicenseToCustomer
                try:
                    cs = AssignLicenseToCustomer.objects.filter(customer_id=project.company_id.uuid).first()
                    if not cs:
                        return Response(
                            responsedata(False, f"No customer licenses found for composer {composer.product_name}"),
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    composer_count = cs.composer_customer.filter(composer_customer_for__id=composer_id).first()
                    if not composer_count:
                        return Response(
                            responsedata(False, f"No customer license found for composer {composer.product_name}"),
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    if int(composer_count.count) < int(count):
                        return Response(
                            responsedata(False, f"You don't have available license for composer {composer.product_name}. Available: {composer_count.count}, Requested: {count}"),
                            status=status.HTTP_400_BAD_REQUEST
                        )
                except Exception as e:
                    return Response(
                        responsedata(False, f"Error validating customer licenses: {str(e)}"),
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Allocate license
                result = allocate_composer_license_to_project(project_id, composer_id, count)
                results.append({
                    "composer_id": composer_id,
                    "composer_name": composer.product_name,
                    "result": result
                })

            return Response(
                responsedata(True, "Composer licenses allocated successfully", results),
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                responsedata(False, f"Error allocating composer licenses: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ProjectList(generics.ListAPIView):
    """to list project, search and pagination implementation"""

    search_fields = ["project_name"]
    filter_backends = (filters.SearchFilter,)

    def get_queryset(self, role, user):
        if role == "superadmin":
            return Project.objects.filter(~Q(is_delete=1)).order_by("project_name")


        elif role == "customeradmin":
            company_id = list(
                UserSettings.objects.filter(uuid=user).values_list('customers', flat=True)
            )[0]
            return Project.objects.filter(Q(company_id=company_id) & ~Q(is_delete=1)).order_by(
                "project_name"
            )
        elif role == "projectadmin":
            return Project.objects.filter(Q(project_admin=user) & ~Q(is_delete=1)).order_by(
                "project_name"
            )
        else:
            return Response(
                responsedata(False, "You are Unauthorized"),
                status=status.HTTP_400_BAD_REQUEST,
            )

    def filter_queryset(self, queryset):
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(self.request, queryset, self)
        return queryset

    @data_response
    def get(self, request, **kw):

        if request.auth is None:
            return Response(
                responsedata(False, "You are Unauthorized"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        user = request.userAuth

        # qs = self.filter_queryset(self.get_queryset(user["role"], user["uuid"]))
        instance = self.filter_queryset(self.get_queryset(user.role, user.uuid))

        columns = dict(
            user_count='total_users',
            uuid='project_uuid',
            company_id_id='company_uuid',
            company_admin_id_id='company_admin_id',
            company_admin_name='company_admin'
        )
        fields = ['startDate', 'project_name', 'total_course', 'status', 'project_admin_id', 'project_admin_name',
                  'company_name']
        fields.extend(columns.keys())

        df = ProjectSummarySerializer(instance, fields=fields, many=True).df.rename(
            index=str, columns=columns)
        # df["created_at"] = df["created_at"].dt.strftime("%d/%m/%Y")
        new_project_list = df.to_dict(orient='records')

        pagenumber = request.GET.get("page", 0)
        if pagenumber == 0:
            return JsonResponse(
                {'results': new_project_list, 'status': True},
                safe=False,
            )
        # o = (pagenumber - 1) * 10
        # new_project_list = list(ProjectListSerializer(qs, many=True).data)

        paginator = Paginator(new_project_list, 10)

        if int(pagenumber) > paginator.num_pages:
            raise ValidationError("Not enough pages", code=404)
        try:
            previous_page_number = paginator.page(pagenumber).previous_page_number()
        except EmptyPage:
            previous_page_number = None
        try:
            next_page_number = paginator.page(pagenumber).next_page_number()
        except EmptyPage:
            next_page_number = None
        data_to_show = paginator.page(pagenumber).object_list

        return JsonResponse(
            {
                "pagination": {
                    "previous_page": previous_page_number,
                    "is_previous_page": paginator.page(pagenumber).has_previous(),
                    "next_page": next_page_number,
                    "is_next_page": paginator.page(pagenumber).has_next(),
                    "start_index": paginator.page(pagenumber).start_index(),
                    "end_index": paginator.page(pagenumber).end_index(),
                    "total_entries": paginator.count,
                    "total_pages": paginator.num_pages,
                    "page": int(pagenumber),
                },
                "results": data_to_show,
                'status': True
            },
            safe=False,
        )


class LicenseList(APIView):
    

    def get(self, request):

        if request.auth is None:
            return Response(responsedata(False, "You are Unauthorized"), status=status.HTTP_400_BAD_REQUEST)

        user = UserSettings.objects.filter(userName=request.user.username).first()

        if user.role == "superadmin":
            project_list = list(Project.objects.all().values().order_by("project_name"))

        elif user.role == "customeradmin":

            company_id = list(UserSettings.objects.filter(uuid=user.uuid).values_list('customers', flat=True))
            project_list = list(Project.objects.filter(company_id__in=company_id).values().order_by("project_name"))
        elif user.role == "projectadmin":
            project_list = list(Project.objects.filter(project_admin=user.uuid).values('uuid', 'project_name').distinct())

        else:
            return Response(responsedata(False, "You are Unauthorized For This Action"), status=status.HTTP_400_BAD_REQUEST)

        new_project_list = []
        for project in project_list:
            new_project = {}

            new_project["project_uuid"] = project["uuid"]
            new_project["project_name"] = project["project_name"]

            new_project_list.append(new_project)

        return Response(responsedata(True, "Project list", new_project_list), status=status.HTTP_200_OK)


class ProjectDetail(APIView):
    """To view, update or delete a project"""

    def delete(self, request, pk):
        project = Project.objects.filter(uuid=pk)
        project.delete()
        return Response(
            responsedata(True, "Project Deleted"), status=status.HTTP_200_OK
        )

    def get(self, request, pk):
        def dl(data):
            return json.loads(json.dumps(data, default=str))

        project_qs = Project.objects.filter(uuid=pk).first()
        serializer = ProjectSummarySerializer(project_qs)
        project = serializer.data

        # Assessments
        assessments = dl(list(Assessment.objects.values('uuid', 'name')))
        assessment_licenses = dl(list(AssessmentLicense.objects.filter(
            project_id=pk
        ).values('assessment_id', 'assessment_id__name', 'count')))

        # Apprenticeships
        apprenticeships = dl(list(Apprenticeship.objects.values('uuid', 'name')))
        apprenticeship_licenses = dl(list(ApprenticeshipLicense.objects.filter(
            project_id=pk
        ).values('apprenticeship_id', 'apprenticeship__name', 'count')))

        lab_licenses = dl(list(LabLicense.objects.filter(project_id=pk).values('lab_id', 'lab_id__name', 'count')))

        new_course_licenses = None
        if project_qs:
            new_course_licenses = CourseLicenseDetailSerializer(
                project_qs.course_licenses.all(), many=True
            ).df.rename(index=str, columns={'users': 'user'}).to_dict(orient='records')
        
        # project = project[0]
        project["project_id"] = project.pop("uuid")
        project['assessments'] = assessments
        project['apprenticeships'] = apprenticeships
        project["assign_licenses"] = {
            "course": new_course_licenses,
            'assessment': assessment_licenses,
            'apprenticeship': apprenticeship_licenses,
            'lab': lab_licenses,
        }
        project["allocate_users"] = {"course": new_course_licenses}
        # customer_id = Project.objects.filter(uuid=project_qs.uuid).company_id
        course = CourseLicense.objects.filter(project_id=project_qs.uuid)
        assesment = AssessmentLicense.objects.filter(project_id=project_qs.uuid)
        apprenticeship = ApprenticeshipLicense.objects.filter(project=project_qs.uuid)
        lab = LabLicense.objects.filter(project_id=project_qs.uuid)

        # Add composer support
        from main.models import ComposerLicense, ComposerLicenseUser
        composer = ComposerLicense.objects.filter(project_id=project_qs.uuid)

        asesment_list = []
        course_list = []
        apprenticeship_list = []
        lab_list = []
        composer_list = []
        course_count = 0
        lab_count = 0
        as_count = 0
        apprenticeship_count = 0
        composer_count = 0
        total_course = 0
        total_assement = 0
        total_apprenticeship = 0
        total_lab = 0
        total_composer = 0

 
        for i in course:
            course_count = int(course_count) + int(i.count)
            total_course = int(total_course) + int(i.initial_count)
            clu_count = CourseLicenseUser.objects.filter(course_license_id=i.uuid).count()

            course_list.append({"uuid": i.course_id.uuid, "name": i.course_id.name, "user_count":clu_count, "count": i.count, "total_count": i.initial_count,
                                "provider": i.course_id.provider})
        for i in lab:
            lab_count = int(lab_count) + int(i.count)
            total_lab = int(total_lab) + int(i.initial_count)

            llu_count = LabLicenseUser.objects.filter(lab_license_id=i.uuid).count()

            lab_list.append({"uuid": i.lab_id.uuid, "name": i.lab_id.name, "user_count":llu_count,  "count": i.count, "total_count": i.initial_count})
        for i in assesment:
            as_count = int(as_count) + int(i.count)
            total_assement = int(total_assement) + int(i.initial_count)
            
            alu_count = AssessmentLicenseUser.objects.filter(assessment_license_id=i.uuid).count()

            asesment_list.append({"uuid": i.assessment_id.uuid, "name": i.assessment_id.name, "user_count":alu_count, "count": i.count, "total_count": i.initial_count})
        for i in apprenticeship:
            apprenticeship_count = int(apprenticeship_count) + int(i.count)
            total_apprenticeship = int(total_apprenticeship) + int(i.initial_count)
            alu_count = ApprenticeshipLicenseUser.objects.filter(apprenticeship_license=i.uuid).count()
            
            apprenticeship_list.append({"uuid": i.apprenticeship.uuid, "name": i.apprenticeship.name, "user_count":alu_count, "count": i.count, "total_count": i.initial_count})

        # Process composers
        for i in composer:
            composer_count = int(composer_count) + int(i.count)
            total_composer = int(total_composer) + int(i.initial_count)
            clu_count = ComposerLicenseUser.objects.filter(composer_license_id=i.uuid).count()

            composer_list.append({"uuid": i.composer_id.id, "name": i.composer_id.product_name, "user_count":clu_count, "count": i.count, "total_count": i.initial_count})

        project["assessment"] = [i for n, i in enumerate(asesment_list) if i not in asesment_list[n + 1:]]
        project["course"] = [i for n, i in enumerate(course_list) if i not in course_list[n + 1:]]
        project["apprenticeship"] = [i for n, i in enumerate(apprenticeship_list) if i not in apprenticeship_list[n + 1:]]
        project["lab"] = [i for n, i in enumerate(lab_list) if i not in lab_list[n + 1:]]
        project["composer"] = [i for n, i in enumerate(composer_list) if i not in composer_list[n + 1:]]
        project["remaining_assesment"] = as_count
        project["total_assesment"] = total_assement
        project["remaining_course"] = course_count
        project["total_course"] = total_course
        project["remaining_apprenticeship"] = apprenticeship_count
        project["remaining_lab"] = lab_count
        project["remaining_composer"] = composer_count
        project["total_apprenticeship"] = total_apprenticeship
        project["total_lab"] = total_lab
        project["total_composer"] = total_composer

        return Response(
            responsedata(True, "Project retrieved successfully", project),
            status=status.HTTP_200_OK,
        )

    def post(self, request, pk):
        from django.http import JsonResponse
        if request.data.get("is_delete") and request.data.get("is_delete") == 1:
            try:
                project = Project.objects.get(uuid=pk)
                project.is_delete = request.data.get("is_delete")
                project.save()
                project_data = json.loads(serializers.serialize('json', [project, ]))

                return JsonResponse({"message": "Project deleted successfully", "status": True, "data": project_data})
            except:
                return JsonResponse({"message": "Project can't deleted", "status": False})
        else:
            return JsonResponse({"message": "Project can't deleted", "status": False})

    def put(self, request, pk):
        if request.data.get("project_name"):
            # Check if project name already exists
            if Project.objects.filter(
                    project_name=request.data.get("project_name")
            ).exists():
                if Project.objects.filter(uuid=pk).values("project_name").first()[
                    "project_name"
                ] != request.data.get("project_name"):
                    return Response(
                        responsedata(False, "Project already exists"),
                        status=status.HTTP_200_OK,
                    )
            r_body = request.data
            if "project_name" in r_body and r_body["project_name"] == "":
                return Response(
                    responsedata(False, "Fill in Project Name"), status=status.HTTP_400_BAD_REQUEST
                )
            elif "company_id" in r_body and r_body["company_id"] == None or r_body["company_id"] == "None":
                return Response(
                    responsedata(False, "Select Company  Name"), status=status.HTTP_400_BAD_REQUEST
                )
            # Saving endDate if status is complete
            if request.data.get("status") == "Complete":
                request.data["endDate"] = datetime.date.today()
            if request.data.get("status") == "open":
                request.data["endDate"] = None
            # Checking Customer and Customer Admin
            # if we got none in then it should be considered as null
            if request.data.get('project_admin') == "None":
                request.data['project_admin'] = None

            if request.data.get('company_admin_id') == "None":
                request.data['company_admin_id'] = None
            # Checking Customer and Customer Admin
            try:
                UserSettings.objects.get(
                    uuid=request.data.get("company_admin_id"),
                    customers=request.data["company_id"],
                )
            except:
                pass
                # return Response(
                #     responsedata(False, "Company Admin Error"),
                #     status=status.HTTP_400_BAD_REQUEST,
                # )
            project_data = request.data.copy()
            course_data = project_data.pop('course') if 'course' in project_data else None
            assessment_data = project_data.pop('assessment') if 'assessment' in project_data else None
            appren_data = project_data.pop('apprenticeship') if 'apprenticeship' in project_data else None
            lab_data = project_data.pop('lab', None)
            composer_data = project_data.pop('composer', None)
            # Updating Project
            project = Project.objects.filter(uuid=pk).first()
            serializer = ProjectSerializer(project, data=project_data, partial=True)
            if serializer.is_valid(raise_exception=True):
                serializer.save()

            # Updating Courses
            try:
                if course_data is not None and type(course_data) in (list, tuple):
                    if len(course_data[0]['course_id']) > 1:
                        # if course_data[0]['course_id'] == None
                        course_ids = []
                        for each_course in course_data:
                            each_course['project_id'] = project.uuid
                            # Updating existing courses in Project
                            l = ['course_id', 'project_id']
                            cl_id_kwargs = {f"{k}_id": v for k, v in each_course.items() if k in l}
                            ccreate, c = CourseLicense.objects.get_or_create(**cl_id_kwargs)
                            if ccreate.count == each_course["count"]:
                                continue
                            else:
                                t_count = int(ccreate.initial_count) + int(each_course["count"])
                                ccreate.initial_count = int(each_course["count"]) #t_count
                                count = each_course.get('count', 1)
                                remainin_count = int(ccreate.count) + int(count)
                                ccreate.count = remainin_count
                                ccreate.save()
            except:
                pass

            # Create Project assessments
            try:

                if assessment_data is not None and type(assessment_data) in (list, tuple):
                    if len(assessment_data[0]['assessment_id']) > 1:
                        for assessment in assessment_data:
                            if assessment["assessment_id"] == "" or assessment["assessment_id"] == "None" or assessment[
                                "assessment_id"] == None:
                                continue
                            if 'assessment_id' in assessment and project:
                                count = assessment.get('count', 1)
                                data = {
                                    "assessment_id_id": assessment["assessment_id"],
                                    "project_id_id": project.uuid,
                                }

                                data_count = {
                                    "count": count if str(count).isdigit() else 1,
                                }
                                qs = AssessmentLicense.objects.filter(**data)
                                if qs.exists():
                                    assesment = qs.first()
                                    if int(assesment.count) == int(count):
                                        continue
                                    else:
                                        remainin_count = int(assesment.count) + int(count)
                                        assesment.count = remainin_count
                                        total_count = int(assesment.initial_count) + int(count)
                                        assesment.initial_count = int(count) #total_count
                                        assesment.save()
                                else:
                                    data_count1 = {
                                        "initial_count": int(count) if str(count).isdigit() else 1,
                                    }
                                    data.update(data_count1)
                                    data.update(data_count)

                                    ap = AssessmentLicense.objects.create(**data)
                                    ap.save()
            except:
                pass
            # Create Project Apprenticeships
            try:
                if appren_data is not None and type(appren_data) in (list, tuple):
                    if len(appren_data[0]['apprenticeship_id']) > 1:
                        for apprenticeship in appren_data:
                            if apprenticeship["apprenticeship_id"] == "None" or apprenticeship[
                                "apprenticeship_id"] == "" or apprenticeship["apprenticeship_id"] == None:
                                continue
                            if 'apprenticeship_id' in apprenticeship and project:
                                count = apprenticeship.get('count', 1)
                                data = {
                                    "apprenticeship_id": apprenticeship["apprenticeship_id"],
                                    "project_id": project.uuid,
                                }
                                data_count = {
                                    "count": count if str(count).isdigit() else 1,
                                }
                                qs = ApprenticeshipLicense.objects.filter(**data)
                                if qs.exists():
                                    app = qs.first()
                                    if int(app.count) == int(count):
                                        continue
                                    else:
                                        remainin_count = int(app.count) + int(count)
                                        app.count = remainin_count
                                        total_count = int(app.initial_count) + int(count)
                                        app.initial_count = int(count) #total_count
                                        app.save()
                                else:
                                    data_count1 = {
                                        "initial_count": int(count) if str(count).isdigit() else 1,
                                    }
                                    data.update(data_count1)
                                    data.update(data_count)
                                    ap = ApprenticeshipLicense.objects.create(**data)
                                    ap.save()
            except:
                pass
            # Create Project Labs
            try:
                if lab_data is not None and type(lab_data) in (list, tuple):
                    if len(lab_data[0]['lab_id']) > 1:
                        for lab in lab_data:
                            if lab["lab_id"] in ["None", "", None]:
                                continue
                            if 'lab_id' in lab and project:
                                count = lab.get('count', 1)
                                data = {
                                    "lab_id_id": lab["lab_id"],
                                    "project_id_id": str(project.uuid),
                                }
                                data_count = {
                                    "count": count if str(count).isdigit() else 1,
                                }
                                qs = LabLicense.objects.filter(**data)
                                if qs.exists():
                                    app = qs.first()
                                    if int(app.count) == int(count):
                                        continue
                                    else:
                                        remainin_count = int(app.count) + int(count)
                                        app.count = remainin_count
                                        total_count = int(app.initial_count) + int(count)
                                        app.initial_count = int(count) #total_count
                                        app.save()
                                else:
                                    # data_count1 = {
                                    #     "initial_count": int(count) if str(count).isdigit() else 1,
                                    # }
                                    # data.update(data_count1)
                                    data.update(data_count)
                                    lb = LabLicense.objects.create(**data)
                                    lb.save()
            except:
                pass

            # Create Project Composers
            try:
                if composer_data is not None and type(composer_data) in (list, tuple):
                    if len(composer_data) > 0 and str(composer_data[0].get('composer_id', '')).strip() != '':
                        for composer in composer_data:
                            if str(composer["composer_id"]).strip() in ["None", "", "0"]:
                                continue
                            if 'composer_id' in composer and project:
                                count = composer.get('count', 1)

                                # Validate customer license availability
                                try:
                                    from main.models import AssignLicenseToCustomer, ComposerCustomer, ComposerLicense
                                    cs = AssignLicenseToCustomer.objects.filter(customer_id=project.company_id.uuid).first()
                                    if cs:
                                        composer_count = cs.composer_customer.filter(composer_customer_for__id=composer["composer_id"]).first()
                                        if not composer_count or int(composer_count.count) < int(count):
                                            return Response(
                                                responsedata(False, f"You don't have available license for composer. Available: {composer_count.count if composer_count else 0}, Requested: {count}"),
                                                status=status.HTTP_400_BAD_REQUEST
                                            )
                                except Exception as e:
                                    return Response(
                                        responsedata(False, f"Error validating customer licenses: {str(e)}"),
                                        status=status.HTTP_400_BAD_REQUEST
                                    )

                                data = {
                                    "composer_id": composer["composer_id"],
                                    "project_id": project,
                                }
                                data_count = {
                                    "count": count if str(count).isdigit() else 1,
                                }
                                qs = ComposerLicense.objects.filter(**data)
                                if qs.exists():
                                    comp = qs.first()
                                    # Update the license count (don't skip if counts are equal)
                                    comp.count = int(count)
                                    comp.initial_count = int(count)
                                    comp.save()
                                else:
                                    data_count1 = {
                                        "initial_count": int(count) if str(count).isdigit() else 1,
                                    }
                                    data.update(data_count1)
                                    data.update(data_count)
                                    cp = ComposerLicense.objects.create(**data)
                                    cp.save()
            except Exception as e:
                print(f"Error handling composer allocation: {str(e)}")
                return Response(
                    responsedata(False, f"Error handling composer allocation: {str(e)}"),
                    status=status.HTTP_400_BAD_REQUEST
                )

            project_data["uuid"] = pk
            project_obj = Project.objects.filter(uuid=pk).first()
            if request.data.get('remove_courses'):
                for i in project_obj.course_licenses.all():
                    if str(i.course_id.uuid) in request.data.get('remove_courses'):
                        check_course_associate_user = CourseLicenseUser.objects.filter(
                            course_license_id=i.uuid).first()
                        if check_course_associate_user:
                            return Response(
                                responsedata(True, message="Course is associated with user",
                                             data=UserSettingsSerializer(check_course_associate_user.user_id).data),
                                status=status.HTTP_400_BAD_REQUEST)
                        course = CourseLicense.objects.filter(project_id=project_obj.uuid,
                                                                  course_id=i.course_id.uuid)
                        course.delete()
            if request.data.get('remove_apprenticeships'):
                for i in project_obj.apprenticeship_licenses.all():
                    if str(i.apprenticeship.uuid) in request.data.get('remove_apprenticeships'):
                        check_apprenticeship_associate_user = ApprenticeshipLicenseUser.objects.filter(
                            apprenticeship_license=i.uuid).first()
                        if check_apprenticeship_associate_user:
                            return Response(
                                responsedata(True, message="Apprenticeships is associated with user",
                                             data=UserSettingsSerializer(check_apprenticeship_associate_user.user).data),
                                status=status.HTTP_400_BAD_REQUEST)
                        apprenticeship = ApprenticeshipLicense.objects.filter(project=project_obj.uuid,
                                                                  apprenticeship=i.apprenticeship.uuid)
                        apprenticeship.delete()
            if request.data.get('remove_assesments'):
                for i in project_obj.assessment_licenses.all():
                    if str(i.assessment_id.uuid) in request.data.get('remove_assesments'):
                        check_assesment_associate_user = AssessmentLicenseUser.objects.filter(
                            assessment_license_id=i.uuid).first()
                        if check_assesment_associate_user:
                            return Response(
                                responsedata(True, message="Assessment is associated with user",
                                             data=UserSettingsSerializer(check_assesment_associate_user.user_id).data),
                                status=status.HTTP_400_BAD_REQUEST)
                        assessment = AssessmentLicense.objects.filter(project_id=project_obj.uuid,
                                                                  assessment_id=i.assessment_id.uuid)
                        assessment.delete()
            if request.data.get('remove_labs'):
                for i in project_obj.lab_licenses.all():
                    if str(i.lab_id.uuid) in request.data.get('remove_labs'):
                        check_labs_associate_user = LabLicenseUser.objects.filter(
                            lab_license_id=i.uuid).first()
                        if check_labs_associate_user:
                            return Response(
                                responsedata(True, message="Labs is associated with user",
                                             data=UserSettingsSerializer(check_labs_associate_user.user_id).data),
                                status=status.HTTP_400_BAD_REQUEST)
                        lab = LabLicense.objects.filter(project_id=project_obj.uuid,
                                                                  lab_id=i.lab_id.uuid)
                        lab.delete()

            # Handle composer removal
            if request.data.get('remove_composers'):
                from main.models import ComposerLicense, ComposerLicenseUser
                for i in ComposerLicense.objects.filter(project_id=project_obj.uuid):
                    if str(i.composer_id.id) in request.data.get('remove_composers'):
                        check_composer_associate_user = ComposerLicenseUser.objects.filter(
                            composer_license_id=i.uuid).first()
                        if check_composer_associate_user:
                            return Response(
                                responsedata(True, message="Composer is associated with user",
                                             data=UserSettingsSerializer(check_composer_associate_user.user_id).data),
                                status=status.HTTP_400_BAD_REQUEST)
                        composer = ComposerLicense.objects.filter(project_id=project_obj.uuid,
                                                                  composer_id=i.composer_id.id)
                        composer.delete()

            return Response(
                responsedata(True, "Project updated successfully", project_data),
                status=status.HTTP_200_OK,
            )


class LicenseDetail(APIView):
    """to get license detail in update license for user view"""

    @data_response
    def put(self, request, pk, *args, **kwargs):
        assessment_delete = request.data.get('assessments', [])
        ret = {}
        c = request.data.get('courses', [])
        # Course
        try:
            if len(c) > 0:
                ret['courses'] = CourseLicense.objects.filter(
                    project_id=pk,
                    course_id__in=[a.get("course_id") for a in c]
                ).delete()

        except Exception as exc:
            logger.exception(exc)
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Assessment
        try:
            if len(assessment_delete) > 0:
                ret['assessments'] = AssessmentLicense.objects.filter(
                    project_id=pk,
                    assessment_id__in=[a.get("assessment_id") for a in assessment_delete]
                ).delete()

        except Exception as exc:
            logger.exception(exc)
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Apperanticeships
        try:
            apprenticeships_delete = request.data.get('apprenticeships', [])
            if len(apprenticeships_delete) > 0:
                ret['apprenticeships'] = ApprenticeshipLicense.objects.filter(
                    project_id=pk,
                    apprenticeship_id__in=[a.get("apprenticeship_id") for a in apprenticeships_delete]).delete()

        except Exception as exc:
            logger.exception(exc)
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # lab
        try:
            lab_delete = request.data.get('labs', [])
            if len(lab_delete) > 0:
                ret['labs'] = LabLicense.objects.filter(
                    project_id=pk,
                    lab_id__in=[a.get("lab_id") for a in lab_delete]).delete()

        except Exception as exc:
            logger.exception(exc)
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Deployments
        try:
            deployments_delete = request.data.get('deployments', [])
            if len(apprenticeships_delete) > 0:
                ret['deployments'] = DeploymentLicense.objects.filter(
                    project_id=pk,
                    deployment_id_id__in=[a.get("deployment_id") for a in deployments_delete]).delete()

        except Exception as exc:
            logger.exception(exc)
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return {'data': ret}

    @data_response
    def get(self, request, pk, *args, **kwargs):
        try:
            proj = get_object_or_404(Project, pk=pk)
            ser = ProjectViewSerializer(get_object_or_404(Project, pk=pk),
                                        fields=[
                                            'users',
                                        ])
            #  {k.replace('_license', ''): v for k, v in ser.data.items()})
            skwargs = dict(many=True, fields=['uuid', 'cl_uuid', 'users', 'count', 'user_count','left_count',
                                              'course_id', 'course_name',
                                              'assessment_id', 'assessment_name',
                                              'apprenticeship_id', 'apprenticeship_name',
                                              'lab_id_id', 'lab_name',
                                              ])
            if 'simple' in kwargs:
                skwargs['fields'] = ['uuid', 'users', 'count', 'product_id', 'product_name', 'product_type']
            # if 'all' in kwargs:
            #     skwargs.pop('fields')
            result = {
                'courses': CourseLicenseDetailSerializer(proj.course_licenses.all(), **skwargs).data,
                'assessments': AssessmentLicenseDetailSerializer(proj.assessment_licenses.all(), **skwargs).data,
                'apprenticeships': ApprenticeshipLicenseSerializer(proj.apprenticeship_licenses.all(), **skwargs).data,
                'labs': LabLicenseSerializer(proj.lab_licenses.all(), **skwargs).data
            }
            result.update(ser.data)
            return Response(
                responsedata(True, "Project retrieved successfully",
                             result),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                responsedata(False, "Project not found"),
                status=status.HTTP_404_NOT_FOUND
            )


class CourseCatalog(APIView):
    """To get course catalog for user view"""

    def get(self, request):
        if request.auth is None:
            return Response(
                responsedata(False, "You are Unauthorized"),
                status=status.HTTP_400_BAD_REQUEST,
            )
        user = request.user

        try:
            token = base64.b64encode(
                (str(get_impersonate_token(user.username))).encode("utf-8")
            )
        except:
            return Response(
                responsedata(False, "You are Unauthorized"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        user = UserSettings.objects.filter(userName=user.username).values().first()
        courses = Course.objects.all().values()

        # Creating list of categories
        category_list= list(Course.objects.values_list('category', flat=True).distinct().order_by('category'))


        # User Courses
        course_licenses_ids = CourseLicenseUser.objects.filter(
            user_id=user["uuid"]
        ).values("course_license_id")
        user_courses = []
        for course_licenses_id in course_licenses_ids:
            course_id = (
                CourseLicense.objects.filter(
                    uuid=course_licenses_id["course_license_id"]
                )
                    .values("course_id")
                    .first()["course_id"]
            )
            user_courses.append(course_id)

        # Arranging courses, category-wise
        catalog = []
        for category in category_list:

            courses_list = []
            for course in courses:

                # Unlocking user's assigned courses
                course["locked"] = False if course["uuid"] in user_courses else True
                if course["uuid"] in user_courses:
                    course["token"] = token

                if category == course["category"]:
                    courses_list.append(course)

            catalog.append({"category": category, "courses": courses_list})

        # Moving Master Programs on top
        for item in catalog:
            if item["category"] == "Master Programs":
                catalog.insert(0, catalog.pop(catalog.index(item)))

        return Response(
            responsedata(True, "Course Catalog retrieved successfully", catalog),
            status=status.HTTP_200_OK,
        )




class CourseCatalogForWeb(APIView):
    authentication_classes = []
    """To get course catalog for user view"""

    def get(self, request):
        # if request.auth is None:
        #     return Response(
        #         responsedata(False, "You are Unauthorized"),
        #         status=status.HTTP_400_BAD_REQUEST,
        #     )
        user = request.user

        try:
            token = base64.b64encode(
                (str(get_impersonate_token(user.username))).encode("utf-8")
            )
        except:
            return Response(
                responsedata(False, "You are Unauthorized"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        user = UserSettings.objects.filter(userName=user.username).values().first()
        if request.GET.get("name"):
            courses = Course.objects.filter(e_commerce=True, name__icontains=request.GET.get("name"))
        else:
            courses = Course.objects.filter(e_commerce=True)
        
        if request.GET.get("category"):
            courses = courses.filter(category=request.GET.get("category"))

        if request.GET.get("course_type"):
            courses = courses.filter(course_type=request.GET.get("course_type"))

        if request.GET.get("accreditation"):
            courses = courses.filter(accredition__icontains=request.GET.get("accreditation"))

        if request.GET.get("popular") == "true":
            courses = courses.filter(popular=True)
        
        courses = courses.values()

        # Creating list of categories
        category_list = []
        for course in courses:
            category_list.append(course["category"])
        category_list = list(set(category_list))

        # User Courses
        course_licenses_ids = CourseLicenseUser.objects.filter(user_id__uuid=user.get("uuid","")).values("course_license_id") if user else []
        user_courses = []
        for course_licenses_id in course_licenses_ids:
            course_id = (
                CourseLicense.objects.filter(
                    uuid=course_licenses_id["course_license_id"]
                )
                    .values("course_id")
                    .first()["course_id"]
            )
            user_courses.append(course_id)

        # Arranging courses, category-wise
        catalog = []
        for category in category_list:

            courses_list = []
            for course in courses:

                # Unlocking user's assigned courses
                course["locked"] = False if course["uuid"] in user_courses else True
                if course["uuid"] in user_courses:
                    course["token"] = token

                if category == course["category"]:
                    courses_list.append(course)

            catalog.append({"category": category, "courses": courses_list})

        # Moving Master Programs on top
        for item in catalog:
            if item["category"] == "Master Programs":
                catalog.insert(0, catalog.pop(catalog.index(item)))


        return Response(
            responsedata(True, "Course Catalog retrieved successfully", catalog),
            status=status.HTTP_200_OK,
        )


class CategoriesCourses(APIView):
    """To get details of all the registered courses of users"""

    def get(self, request):
        categories = Course.objects.filter(e_commerce=True).exclude(course_type="Learning Path").values_list("category", flat=True).distinct().order_by("category")

        return Response(
            responsedata(True, "Course categories retrieved successfully", {"categories": categories}),
            status=status.HTTP_200_OK,
        )


class RegisteredCourses(APIView):
    """To get details of all the registered courses of users"""

    def get(self, request):
        

        if request.auth is None:
            return Response(responsedata(False, "You are Unauthorized"), status=status.HTTP_400_BAD_REQUEST)

        # **********************Ashutosh code***********************
        # try:
        #     token = base64.b64encode((str(get_impersonate_token(request.user.username))).encode("utf-8"))
        # except:
        #     return Response(responsedata(False, "You are Unauthorized"), status=status.HTTP_400_BAD_REQUEST)

        # **********************Ashutosh code***********************

        user = UserSettings.objects.filter(userName=request.user.username).values().first()
        qs = {"user_id": user["uuid"]}
        if request.GET.get("category"):
            qs["course_license_id__course_id__category__icontains"] = request.GET.get("category")
        course_licenses_ids = CourseLicenseUser.objects.filter(**qs).values("pk", "course_license_id", "course_completion")
        qs.pop('course_license_id__course_id__category__icontains',None)
        # Query the LabLicenseUser model and retrieve 'lab_license_id' values as a list
        lab_license_ids = list(LabLicenseUser.objects.filter(**qs).values_list("lab_license_id", flat=True))

        # Query the LabLicense model to get lab_ids based on 'lab_license_id' values
        lab_ids = list(LabLicense.objects.filter(uuid__in=lab_license_ids).values_list("lab_id", flat=True))

        # Serialize the labs
        lab_serializer = LabSerializer(Lab.objects.filter(uuid__in=lab_ids), many=True)

        labs_linked_data = LabCourseLinkedLicense.objects.filter(lab_license__in=lab_ids)

        labs_data = {}
        for row in labs_linked_data:
            for col in lab_serializer.data:
                if col['uuid'] == str(row.lab_license.uuid):
                    if str(row.course.uuid) in labs_data.keys():
                        labs_data[str(row.course.uuid)].append(col)
                    else:
                        labs_data[str(row.course.uuid)] = [col]
        courses = []
        category_list = []
        elearning_states = ElearninStates.objects.filter(user_id=user["uuid"]).values('course_title', 'self_learning_completion')
        elearning_dict = {state['course_title']: state['self_learning_completion'] for state in elearning_states}
        for course_licenses_id in course_licenses_ids:
            try:
                course_id = (
                    CourseLicense.objects.filter(uuid=course_licenses_id["course_license_id"]).values(
                        "course_id").first()[
                        "course_id"])
                course_name = Course.objects.filter(uuid=course_id).first()
                course_completion = course_licenses_id["course_completion"]
                # Override course_completion with data from ElearninStates if available
                course_completion = elearning_dict.get(course_name.name, course_completion)
                # try:
                #     if course_name.course_id_talent_lms:
                #         # Update course completion progress for
                #         # courses provided by TalentLMS

                #         try:
                #             user_id = user['user_id_talentlms']
                #             course_id_lms = course_name.course_id_talent_lms

                #             url = "%s/getuserstatusincourse/course_id:%s,user_id:%s" % (
                #                 URL_ADDRESS, course_id_lms, user_id)

                #             response = requests.request(
                #                 "GET", url, auth=(API_KEY, ''), headers={}, data={})

                #             if response.status_code == 200:
                #                 content = json.loads(response.text)
                #                 course_completion = content['completion_percentage']
                #                 cl = CourseLicenseUser.objects.get(pk=course_licenses_id['pk'])
                #                 cl.course_completion = course_completion
                #                 cl.save()

                #             else:
                #                 logger.critical(response.text)
                #         except Exception as exc:
                #             logger.exception(exc)
                #             pass
                          
                #     if course_name.provider== "Xpert Skills":
                #         try:
                #             # using email and course_title from  main_elearninstates getting the course progress
                #             email = user["email"]
                #             course_title = course_name.name
                #             course_completion = ElearninStates.objects.filter(email = email, course_title = course_title).values_list('self_learning_completion', flat=True).first() # values('self_learning_completion').first()
                #         except Exception as exc:
                #             print(f"Xperts skill course completion -- {exc}")
                #             logger.exception(exc)

                # except Exception as exc:
                #     logger.exception(exc)
                #     pass

                courses.append(
                    {
                        "course_id": course_id,
                        "course_name": course_name.name,
                        "course_completion": course_completion,
                        # "description": course_name["description"],
                        # "course_completion": course_licenses_id["course_completion"],
                        "description": description(course_name.description),
                        "provider": course_name.provider,
                        "link": course_name.link,
                        "category": course_name.category,
                        # "token": token,
                        "course_id_talent_lms": course_name.course_id_talent_lms,
                        "event_url": course_name.event_course.first().url if course_name.event_course.first() else '',
                        "event_start_time": course_name.event_course.first().start_time if course_name.event_course.first() else '',
                        "event_end_time": course_name.event_course.first().end_time if course_name.event_course.first() else '',
                        "event_start_date": course_name.event_course.first().start_date if course_name.event_course.first() else '',
                        "event_end_date": course_name.event_course.first().end_date if course_name.event_course.first() else ''
                    })
                

                if course_name.category not in category_list:
                    category_list.append(course_name.category)
            except Exception as exc:
                logger.exception(exc)
                pass

        # Arrnaging respone category wise
        all_category_wise = []
        for category in category_list:
            category_wise = [] 
            for item in courses:
                if category == item["category"]:
                    category_wise.append({"course":item, 'labs':labs_data.get(str(item['course_id']), [])})
 
            all_category_wise.append({"category": category, "data": category_wise})

        return Response(responsedata(True, "Courses retrieved successfully", {"courses":all_category_wise}),
                        status=status.HTTP_200_OK)


# Reports


class CustomersReport(generics.ListAPIView):
    """To get list of customers, customer admins, projects and users"""

    search_fields = ["name"]
    filter_backends = (filters.SearchFilter,)

    def get_queryset(self):
        return Company.objects.all().order_by("name")

    def filter_queryset(self, queryset):
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(self.request, queryset, self)
        return queryset

    def get(self, request):

        instance = self.filter_queryset(self.get_queryset())

        annot_kwargs = dict(
            projects_count=Count('projects__uuid', distinct=True),
            # admins_count=Sum(Case(
            #     When(Q(company__role="customeradmin"), V(1)), default=V(0),
            #     output_field=IntegerField())),
            # users_count=Sum(Case(
            #     When(Q(company__role="user"), V(1)), default=V(0),
            #     output_field=IntegerField())),
            assessment_available=Count('tmforum_assessment__assessment__responses'),
        )
        customer_list = list(instance.annotate(**annot_kwargs).values())

        for customer in customer_list:

            # Start Date
            customer["created_at"] = customer["created_at"].strftime("%d/%m/%Y")

            # Admins
            customer["admins_count"] = UserSettings.objects.filter(customers=customer["uuid"],
                                                                   role="customeradmin").count()
            if customer["admins_count"] == 1:
                customer["admin_name"] = (
                    UserSettings.objects.filter(customers=customer["uuid"], role="customeradmin").values(
                        "userName").first()["userName"])

            # Users
            customer["users_count"] = UserSettings.objects.filter(
                customers=customer["uuid"], role="user"
            ).count()

        pagenumber = request.GET.get("page", 1)
        paginator = Paginator(customer_list, 10)

        if int(pagenumber) > paginator.num_pages:
            raise ValidationError("Not enough pages", code=404)
        try:
            previous_page_number = paginator.page(pagenumber).previous_page_number()
        except EmptyPage:
            previous_page_number = None
        try:
            next_page_number = paginator.page(pagenumber).next_page_number()
        except EmptyPage:
            next_page_number = None
        data_to_show = paginator.page(pagenumber).object_list

        return JsonResponse(
            {
                "pagination": {
                    "previous_page": previous_page_number,
                    "is_previous_page": paginator.page(pagenumber).has_previous(),
                    "next_page": next_page_number,
                    "is_next_page": paginator.page(pagenumber).has_next(),
                    "start_index": paginator.page(pagenumber).start_index(),
                    "end_index": paginator.page(pagenumber).end_index(),
                    "total_entries": paginator.count,
                    "total_pages": paginator.num_pages,
                    "page": int(pagenumber),
                },
                "results": data_to_show,
            },
            safe=False,
        )


class AdminsReport(generics.ListAPIView):
    """To get list of customer admins"""

    search_fields = ["firstName", "lastName", "email"]
    filter_backends = (filters.SearchFilter,)

    def get_queryset(self, company_id):
        return UserSettings.objects.filter(
            role="customeradmin", customers=company_id
        ).order_by("firstName")

    def filter_queryset(self, queryset):
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(self.request, queryset, self)
        return queryset

    def get(self, request):

        if not request.GET.get("uuid"):
            return Response(responsedata(False, "Customer not provided"), status=status.HTTP_400_BAD_REQUEST)

        instance = self.filter_queryset(self.get_queryset(request.GET.get("uuid")))
        admins_list = list(instance.annotate(
            assessment_available=Count('companyID__tmforum_assessment_responses__uuid')
        ).values())

        for admin in admins_list:

            # Removing unwanted data
            keys_to_remove = [
                "created_at",
                "updated_at",
                "user_id",
                "reset_id",
                "validated",
                "userName",
                "password",
                "location",
                "contact_no",
                "companyID_id",
                "country",
                "role",
            ]
            for key in keys_to_remove:
                del admin[key]

        pagenumber = request.GET.get("page", 1)
        paginator = Paginator(admins_list, 10)

        if int(pagenumber) > paginator.num_pages:
            raise ValidationError("Not enough pages", code=404)
        try:
            previous_page_number = paginator.page(pagenumber).previous_page_number()
        except EmptyPage:
            previous_page_number = None
        try:
            next_page_number = paginator.page(pagenumber).next_page_number()
        except EmptyPage:
            next_page_number = None
        data_to_show = paginator.page(pagenumber).object_list

        return JsonResponse(
            {
                "pagination": {
                    "previous_page": previous_page_number,
                    "is_previous_page": paginator.page(pagenumber).has_previous(),
                    "next_page": next_page_number,
                    "is_next_page": paginator.page(pagenumber).has_next(),
                    "start_index": paginator.page(pagenumber).start_index(),
                    "end_index": paginator.page(pagenumber).end_index(),
                    "total_entries": paginator.count,
                    "total_pages": paginator.num_pages,
                    "page": int(pagenumber),
                },
                "results": data_to_show,
            },
            safe=False,
        )


class ProjectsReport(generics.ListAPIView):
    """To get details of projects as per customer"""

    search_fields = ["project_name"]
    filter_backends = (filters.SearchFilter,)

    def get_queryset(self, **kwargs):
        return Project.objects.filter(**kwargs).order_by("project_name")

    def filter_queryset(self, queryset):
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(self.request, queryset, self)
        return queryset

    def get(self, request):
        ident = {'company_id' if k == 'uuid' else k: request.GET.get(k) for k in ['uuid', 'project_id', 'company_id'] if
                 request.GET.get(k, None)}
        if len(ident) == 0:
            return Response(
                responsedata(False, "Customer not provided"),
                status=status.HTTP_400_BAD_REQUEST,
            )
        if 'project_id' in ident:
            ident['pk'] = ident.pop('project_id')
        instance = self.filter_queryset(self.get_queryset(**ident))
        df = ProjectSummarySerializer(instance, many=True).df.rename(index=str, columns=dict(
            company_admin_id_id='company_admin_id'
        ))
        data_list = []
        if not df.empty:
            if 'created_at' in df.columns.tolist():
                df["created_at"] = df["created_at"].apply(
                    lambda x: x.strftime("%d/%m/%Y") if type(x) in (datetime.datetime, datetime.date) else str(x))
            data_list = df.to_dict(orient='records')

        # Pagination
        pagenumber = request.GET.get("page", 0)
        if pagenumber == 0:
            return JsonResponse(
                {'results': data_list, 'status': True},
                safe=False,
            )
        paginator = Paginator(data_list, 10)

        if int(pagenumber) > paginator.num_pages:
            raise ValidationError("Not enough pages", code=404)
        try:
            previous_page_number = paginator.page(pagenumber).previous_page_number()
        except EmptyPage:
            previous_page_number = None
        try:
            next_page_number = paginator.page(pagenumber).next_page_number()
        except EmptyPage:
            next_page_number = None
        data_to_show = paginator.page(pagenumber).object_list

        return JsonResponse(
            {
                "pagination": {
                    "previous_page": previous_page_number,
                    "is_previous_page": paginator.page(pagenumber).has_previous(),
                    "next_page": next_page_number,
                    "is_next_page": paginator.page(pagenumber).has_next(),
                    "start_index": paginator.page(pagenumber).start_index(),
                    "end_index": paginator.page(pagenumber).end_index(),
                    "total_entries": paginator.count,
                    "total_pages": paginator.num_pages,
                    "page": int(pagenumber),
                },
                'status': True,
                "results": data_to_show,
            },
            safe=False,
        )


class UsersReport(generics.ListAPIView):
    """To get details of users as per customer's project"""

    search_fields = ["user_id__firstName", "user_id__lastName", "user_id__email"]
    filter_backends = (filters.SearchFilter,)

    def get_queryset(self, project_id):
        clu_ids = []
        user_ids = []
        course_licenses = CourseLicense.objects.filter(project_id=project_id).values()
        for course_license in course_licenses:
            cl_users = CourseLicenseUser.objects.filter(
                course_license_id=course_license["uuid"]
            ).values()
            for user in cl_users:
                if user["user_id_id"] not in user_ids:
                    clu_ids.append(user["uuid"])
                    user_ids.append(user["user_id_id"])

        return CourseLicenseUser.objects.filter(uuid__in=clu_ids).order_by(
            "user_id__firstName"
        )

    def filter_queryset(self, queryset):
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(self.request, queryset, self)
        return queryset

    def get(self, request):

        if not request.GET.get("uuid"):
            return Response(
                responsedata(False, "Project not provided"),
                status=status.HTTP_400_BAD_REQUEST,
            )
        value_args = [
            'user_id__uuid',
            'user_id__firstName',
            'user_id__lastName',
            'user_id__email',
            'user_id__profile_image',
            'course_license_id__project_id__project_name',
            'course_license_id__course_id__name',
            'course_completion'
        ]

        def clean_key(x):
            for k in [
                'user_id__',
                'course_license_id__project_id__',
                'license_id__course_id__'
            ]:
                x = x.replace(k, '')
            return x.lower()

        rename_cols = {k: clean_key(k) for k in value_args}
        instance = self.filter_queryset(self.get_queryset(request.GET.get("uuid")))
        user_list = pd.DataFrame(instance.values(*value_args)).rename(
            index=str,
            columns=rename_cols
        )  # .groupby('uuid', as_index=False).agg({'course_name': 'unique'})
        user_list = user_list.fillna(value=0).to_dict(orient='records')
        # Pagination
        pagenumber = request.GET.get("page", 0)
        if pagenumber == 0:
            return JsonResponse(
                {'results': user_list, 'status': True},
                safe=False,
            )
        paginator = Paginator(user_list, 10)

        if int(pagenumber) > paginator.num_pages:
            raise ValidationError("Not enough pages", code=404)
        try:
            previous_page_number = paginator.page(pagenumber).previous_page_number()
        except EmptyPage:
            previous_page_number = None
        try:
            next_page_number = paginator.page(pagenumber).next_page_number()
        except EmptyPage:
            next_page_number = None
        data_to_show = paginator.page(pagenumber).object_list

        return JsonResponse(
            {
                "pagination": {
                    "previous_page": previous_page_number,
                    "is_previous_page": paginator.page(pagenumber).has_previous(),
                    "next_page": next_page_number,
                    "is_next_page": paginator.page(pagenumber).has_next(),
                    "start_index": paginator.page(pagenumber).start_index(),
                    "end_index": paginator.page(pagenumber).end_index(),
                    "total_entries": paginator.count,
                    "total_pages": paginator.num_pages,
                    "page": int(pagenumber),
                },
                "results": data_to_show,
            },
            safe=False,
        )


class UserDetailReport(APIView):
    """To get simplilearn's data"""

    def post(self, request):

        if not request.data.get("user_id") and not request.data.get("project_id"):
            return Response(responsedata(False, "User or project details not provided"),
                            status=status.HTTP_400_BAD_REQUEST)
        from django.db.models.functions import Concat
        user_id = request.data.get("user_id")
        project_id = request.data.get("user_id")
        user = UserSettings.objects.filter(
            uuid=request.data.get("user_id")
        ).annotate(
            username=F('userName'),
            fullname=Concat(F('firstName'), F('lastName')),
            member_since=F('created_at')
        ).values(
            *list('uuid,username,fullname,member_since,email,profile_image'.split(','))
        ).first()
        customers = Project.objects.filter(uuid=request.data.get("project_id")).annotate(
            company_uuid=F('company_id'),
            company_name=F('company_id__name'),
            company_country=F('company_id__country'),
        ).values(
            'company_uuid', 'company_name', 'company_country'
        ).first()

        # User Data
        data = user.copy()
        data.update(customers)
        data["member_since"] = data["member_since"].strftime("%d/%m/%Y")
        data["skills_transformation"] = []
        # User Courses Data
        annot_kw = dict(
            course_uuid=F("course_license_id__course_id"),
            course_name=F("course_license_id__course_id__name"),
            course_provider=F("course_license_id__course_id__provider"),
            course_category=F("course_license_id__course_id__category"),
            courseID=F("course_license_id__course_id__course_id"),
            course_type=F("course_license_id__course_id__course_type"),
        )
        value_args = ["course_license_id", "course_completion"]
        value_args.extend(annot_kw.keys())
        course_licenses = pd.DataFrame(CourseLicenseUser.objects.filter(
            user_id=user_id,
            course_license_id__project_id=project_id,
        ).annotate(
            **annot_kw
        ).values(*value_args))
        if not course_licenses.empty:
            course_licenses = course_licenses.fillna(value=0).sort_values('course_category')

            # Category list
            category_data = {k: [] for k in course_licenses.course_category.unique().tolist()}

            def g(group):
                return {
                    'average': group['score'].mean(),
                    'category': group['course_category'].tolist()[0],
                    'data': group.to_dict(orient='records')
                }

            all_category_wise = course_licenses.groupby('course_category', as_index=False).apply(g)
            #
            # # Arrnaging respone category wise
            # all_category_wise = []
            # for category in category_list:
            #     category_wise = []
            #     score = []
            #     for item in course_licenses:
            #         if category == item["course_category"]:
            #             if "course_completion" in item:
            #                 if item["course_completion"] is None:
            #                     item["course_completion"] = 0
            #                 score.append(item["course_completion"])
            #             else:
            #                 item["course_completion"] = 0
            #                 score.append(item["course_completion"])
            #             category_wise.append(item)
            #
            #     if len(score) != 0:
            #         score = sum(score) / len(score)
            #     else:
            #         score = 0
            #
            #     all_category_wise.append(
            #         {
            #             "category": category,
            #             "average": round(score, 2),
            #             "data": category_wise,
            #         }
            #     )

            data["skills_transformation"] = all_category_wise.to_dict(orient='records')

        return Response(
            responsedata(True, "Report retrieved successfully", data),
            status=status.HTTP_200_OK,
        )


class UserViewProjects(APIView):
    """To get projects allocated to a user"""

    def get(self, request, pk):

        cl_ids = CourseLicenseUser.objects.filter(user_id=pk).values(
            "course_license_id"
        )
        projects = []

        for cl_id in cl_ids:
            cl = (
                CourseLicense.objects.filter(uuid=cl_id["course_license_id"])
                    .values("project_id", "project_id__project_name")
                    .first()
            )
            item = {
                "project_uuid": cl["project_id"],
                "project_name": cl["project_id__project_name"],
            }
            if item in projects:
                continue
            projects.append(item)

        return Response(
            responsedata(True, "Projects retrieved successfully", projects),
            status=status.HTTP_200_OK,
        )


# Download Reports
class DownloadReport(APIView):

    def get(self, request, pk):
        from datetime import datetime

        if not Company.objects.filter(uuid=pk).exists():
            return Response(responsedata(False, "Customer does not exist"), status=status.HTTP_400_BAD_REQUEST)

        if request.query_params.get('project_id'):
            if not Project.objects.filter(uuid=request.query_params.get('project_id')).exists():
                return Response(responsedata(False, "Project does not exist"), status=status.HTTP_400_BAD_REQUEST)
            data = CourseLicenseUser.objects.filter(
                course_license_id__project_id=request.query_params.get('project_id'),
                course_license_id__project_id__company_id=pk)

        else:
            data = CourseLicenseUser.objects.filter(course_license_id__project_id__company_id=pk)

        data = data.annotate(Customer=Cast("course_license_id__project_id__company_id__name", output_field=TextField()),
                             Project=Cast("course_license_id__project_id__project_name", output_field=TextField()),
                             Admin=Cast("course_license_id__project_id__company_admin_id__userName",
                                        output_field=TextField()),
                             Description=Cast("course_license_id__project_id__description", output_field=TextField()),
                             Status=Cast("course_license_id__project_id__status", output_field=TextField()),
                             StartDate=Cast("course_license_id__project_id__startDate", output_field=TextField()),
                             EndDate=Cast("course_license_id__project_id__endDate", output_field=TextField()),
                             Name=Cast("user_id__userName", output_field=TextField()),
                             Email=Cast("user_id__email", output_field=TextField()),
                             CourseName=Cast("course_license_id__course_id__name", output_field=TextField()),
                             Category=Cast("course_license_id__course_id__category", output_field=TextField()),
                             Provider=Cast("course_license_id__course_id__provider", output_field=TextField()),
                             CourseID=Cast("course_license_id__course_id__course_id", output_field=TextField()),
                             CourseType=Cast("course_license_id__course_id__course_type", output_field=TextField()),
                             Completion=Cast("course_completion", output_field=TextField())).order_by('Project', 'Name',
                                                                                                      'CourseName')

        data = list(data.values('Customer', 'Project', 'Admin', 'Description', 'Status', 'StartDate', 'EndDate', 'Name',
                                'Email', 'CourseName',
                                'Category', 'Provider', 'CourseID', 'CourseType', 'Completion'))

        data = pd.DataFrame(data)
        if data.empty:
            return Response(responsedata(False, "No Data in Query Set", ),
                            status=status.HTTP_404_NOT_FOUND)
        data['CourseID'] = data['CourseID'].apply(safe_int).astype(int)

        more_data = pd.read_csv('https://elearn-stat.s3.amazonaws.com/tmp/useractivity.csv')
        more_data = more_data.dropna(axis=1, how='all')

        more_data = more_data.rename(columns={"Learner Email": "Email", "Course Id": "CourseID"})

        df = pd.merge(data, more_data, how='left')
        df = df.drop(['Learner Name', 'Self-Learning Completion %'], axis=1)

        df = df.dropna(axis=1, how='all')

        filename = f'report_{datetime.now().strftime("%d-%m-%Y")}_{str(randint(1000, 9999))}.csv'
        data = df.to_csv(filename, index=False, header=True)

        with open(filename, "rb") as f:
            data = f.read()
        data = base64.b64encode(data)

        os.remove(filename)

        return Response(responsedata(True, "Report downloaded successfully", data), status=status.HTTP_200_OK)


def getUserDetailsCSV(filter_kwargs):
    from datetime import datetime
    from collections import OrderedDict

    rename = OrderedDict([
        ("user_id__userName", 'Name'),
        ("user_id__email", "Email"),
        ("course_license_id__course_id__name", "CourseName"),
        ("course_completion", "Completion"),
        ("course_license_id__course_id__category", "Category"),
        ("course_license_id__course_id__provider", "Provider"),
        ("course_license_id__course_id__course_id", "CourseID"),
        ("course_license_id__course_id__course_type", "CourseType"),
        ("course_license_id__project_id__company_id__name", "Customer"),
        ("course_license_id__project_id__project_name", "Project"),
        ("course_license_id__project_id__company_admin_id__userName", "Admin"),
        ("course_license_id__project_id__description", "Description"),
        ("course_license_id__project_id__status", "Status"),
        ("course_license_id__project_id__startDate", "StartDate"),
        ("course_license_id__project_id__endDate", "EndDate"), ]
    )

    qs = CourseLicenseUser.objects.filter(**filter_kwargs).values(*rename.keys()).order_by(
        'course_license_id__project_id__project_name',
        'course_license_id__course_id__category',
        'course_license_id__course_id__name'
    )

    try:

        data = pd.DataFrame(list(qs))
        if data.empty:
            return Response(responsedata(False, "No Data in Query Set", ),
                            status=status.HTTP_404_NOT_FOUND)
        data = data.rename(index=str, columns=rename)
        data['CourseID'] = data['CourseID'].apply(safe_int).astype(int)

        more_data = pd.read_csv('https://elearn-stat.s3.amazonaws.com/tmp/useractivity.csv')
        more_data = more_data.dropna(axis=1, how='all')

        more_data = more_data.rename(columns={"Learner Email": "Email", "Course Id": "CourseID"})

        df = pd.merge(data, more_data, how='left')
        df = df.drop(['Learner Name', 'Self-Learning Completion %'], axis=1)

        df = df.dropna(axis=1, how='all')

        filename = f'report_{datetime.now().strftime("%d-%m-%Y")}_{str(randint(1000, 9999))}.csv'

        r = df.to_csv(filename, index=False, header=True)

        with open(filename, "rb") as f:
            output_data = f.read()
        output_data = base64.b64encode(output_data)

        os.remove(filename)

        return Response(responsedata(True, "Report downloaded successfully", output_data),
                        status=status.HTTP_200_OK)
    except Exception as e:
        import traceback
        traceback.print_stack()
        print('~'.join(['#'] * 50))
        traceback.print_exc()
        return Response(responsedata(False, "Error Downloading"),
                        status=status.HTTP_400_BAD_REQUEST)


class UserDetailedCSV(APIView):

    def get(self, request, pk):
        if not UserSettings.objects.filter(uuid=pk).exists():
            return Response(responsedata(False, "User does not exist"), status=status.HTTP_400_BAD_REQUEST)

        return getUserDetailsCSV(dict(user_id=pk))


class ProjectUserDetailedCSV(APIView):
    @data_response
    def get(self, request, *args, **kwargs):
        project_id = kwargs.get('project_id')
        if type(project_id) not in (tuple, list):
            project_id = [project_id]
        return getUserDetailsCSV(dict(course_license_id__project_id__in=project_id))


class RegisteredAssessments(APIView):
    """To get details of all the registered assessments of users"""

    @data_response
    def patch(self, request, pk, *args, **kwargs):
        # from main.models import GCState
        if request.auth is None:
            return Response(
                responsedata(False, "You are Unauthorized"),
                status=status.HTTP_400_BAD_REQUEST,
            )
        obj = GCIndexAssessment.objects.get(pk=pk)
        if obj.state_id == 9:
            return dict(
                status=True,
                data={'url': obj.url},
                message='GCIndex Loaded'
            )
        if obj.state_id < 6:
            obj.state_id = 10
            obj.save()
        return dict(
            status=True,
            data={'url': obj.url},
            message='GCIndex Loaded'
        )

    @data_response
    def put(self, request, pk, *args, **kwargs):
        # from main.models import GCState
        if request.auth is None:
            return Response(
                responsedata(False, "You are Unauthorized"),
                status=status.HTTP_400_BAD_REQUEST,
            )
        obj = GCIndexAssessment.objects.get(pk=pk)
        if obj.state_id == 9:
            return dict(
                status=True,
                data={'url': obj.url},
                message='GCIndex Loaded'
            )
        if obj.state_id < 6:
            obj.state_id = 10
            obj.save()

        return dict(
            status=True,
            data={'url': obj.url},
            message='GCIndex Loaded'
        )

    @data_response
    def get(self, request, *args, **kwargs):
        if request.auth is None:
            return Response(
                responsedata(False, "You are Unauthorized"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        user = request.userAuth
        assesments_based_on_user = user.project_id.assessment_licenses.all().values_list("assessment_id__name","uuid")

        gc = {}
        tm = {}

        for name,assesment in assesments_based_on_user:
            licence_check = AssessmentLicenseUser.objects.filter(assessment_license_id=assesment,user_id=user).first()
            if name == "Digital Readiness Assessment":
                tm = dict(exists=bool(licence_check), url='/tmforum/1')
            elif name == "GC Index":
                gc = dict(exists=bool(licence_check), url='')

        # qs = GCIndexAssessment.objects.filter(user=user)
        # gc = dict(exists=qs.exists(), url='')
        # # tmqs = TMForumUserAssessment.objects.filter(owner=user)
        # try:
        #     user = UserSettings.objects.get(user=request.user)
        #     all = AssessmentLicenseUser.objects.get(user_id=user)
        #     tm = dict(exists=True, url='/tmforum/1')
        # except:
        #     tm = dict(exists=False, url='/tmforum/1')
        # tm = dict(exists=tmqs.exists(), url='/tmforum/1')
        # if qs.exists():
        #     gc.update(GCIndexAssessmentSerializer(qs.first()).data)
        #     gc['state'] = qs.first().state_id
        return dict(
            status=True,
            data={'gc': gc, 'tm': tm},
            message='Assessments Loaded'
        )

class RegisteredApprenticeships(APIView):
    """To get details of all the registered Apprenticeships of users"""

    @data_response
    def get(self, request, *args, **kwargs):
        if request.auth is None:
            return Response(
                responsedata(False, "You are Unauthorized"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        user = request.userAuth

        qs = ApprenticeshipLicenseUser.objects.filter(user=user).first()
        if qs != None:
            ser = ApprenticeshipLicenseUserDisplaySerializer(qs, partial=True)
            return dict(
                status=True,
                data=[ser.data],
                message='Apprenticeships Loaded'
            )
        else:
            return dict(
                status=True,
                data=[],
                message='Apprenticeships Loaded'
            )


# get users of consume course
class AllocateLicenses(APIView):

    def post(self, request):
        try:
            if "company_id" in request.data and (
                    request.data['company_id'] != "" or request.data['company_id'] != None):
                all_data = CourseLicenseUser.objects.filter(
                    course_license_id__project_id__company_id=request.data['company_id'])
                response_list = []
                for i in all_data:
                    response_body = {}
                    response_body['course'] = i.course_license_id.course_id.name
                    response_body['user'] = i.user_id.userName
                    response_body['customer'] = i.course_license_id.project_id.company_id.name
                    response_body['project'] = i.course_license_id.project_id.project_name
                    response_list.append(response_body)
                return Response(responsedata(True, "All course with user details get succesfully", response_list),
                                status=status.HTTP_200_OK)
            else:
                all_data = CourseLicenseUser.objects.all()
                response_list = []
                for i in all_data:
                    response_body = {}
                    response_body['course'] = i.course_license_id.course_id.name
                    response_body['user'] = i.user_id.userName
                    response_body['customer'] = i.course_license_id.project_id.company_id.name
                    response_body['project'] = i.course_license_id.project_id.project_name
                    response_list.append(response_body)
                return Response(responsedata(True, "All course with user details get succesfully", response_list),
                                status=status.HTTP_200_OK)

        except Exception as ex:
            return Response(
                responsedata(False, "Can't connect to database", ex),
                status=status.HTTP_400_BAD_REQUEST,
            )


# ssh -i pram2.pem ubuntu@34.222.118.114
# python manage.py crontab add / show / remove
# get all projects of customer using id
class AllProjectsByCustomer(APIView):
    
    # def get(self,request):
    #     try:
    #         customer_id = request.GET.get("customer_id")
    #         projects = Project.objects.filter(company_id=customer_id).values()
    #         response_dict = {}
    #         response_dict["projects"] = projects
    #         return Response(responsedata(True, "project summary details fetch sucessfully", response_dict),status=status.HTTP_200_OK)
    #     except Exception as ex:
    #         return Response(responsedata(False, "details are not provided"), status=status.HTTP_200_OK)

    """to list project, search and pagination implementation"""

    search_fields = ["project_name"]
    filter_backends = (filters.SearchFilter,)

    def get_queryset(self, company_id_list):
        return Project.objects.filter(Q(company_id__in=company_id_list) & ~Q(is_delete=1)).order_by("project_name")

    def filter_queryset(self, queryset):
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(self.request, queryset, self)
        return queryset

    def same_call_for_get_and_post(self, request, **kw):
        if request.auth is None:
            return Response(
                responsedata(False, "You are Unauthorized"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        # user = request.userAuth
        # qs = self.filter_queryset(self.get_queryset(user["role"], user["uuid"]))
        instance = self.filter_queryset(self.get_queryset(kw.get("customer_id",[])))

        columns = dict(
            user_count='total_users',
            uuid='project_uuid',
            company_id_id='company_uuid',
            company_admin_id_id='company_admin_id',
            company_admin_name='company_admin'
        )
        # fields = ['startDate', 'project_name', 'total_course', 'status', 'project_admin_id', 'project_admin_name',
        #           'company_name']

        fields = ["uuid", "created_at", "updated_at", "project_name", "company_id_id", "superadmin_id_id",
                  "company_admin_id_id", "project_admin_id", "description", "status", "admin_count","apprenticeship_count","user_count","assessment_count","course_count","startDate", "endDate",
                  "is_delete", "company_name", "company_admin", "total_users", "total_course", "total_assessments",
                  "total_experiments", "total_deployments", "project_uuid", "company_uuid"]

        fields.extend(columns.keys())

        df = ProjectSummarySerializer(instance, fields=fields, many=True).df.rename(
            index=str, columns=columns)
        # df["created_at"] = df["created_at"].dt.strftime("%d/%m/%Y")
        new_project_list = df.to_dict(orient='records')

        pagenumber = request.GET.get("page", 0)
        if pagenumber == 0:
            return JsonResponse(
                {'results': new_project_list,'status': True},
                safe=False,
            )
        # o = (pagenumber - 1) * 10
        # new_project_list = list(ProjectListSerializer(qs, many=True).data)

        paginator = Paginator(new_project_list, 10)

        if int(pagenumber) > paginator.num_pages:
            raise ValidationError("Not enough pages", code=404)
        try:
            previous_page_number = paginator.page(pagenumber).previous_page_number()
        except EmptyPage:
            previous_page_number = None
        try:
            next_page_number = paginator.page(pagenumber).next_page_number()
        except EmptyPage:
            next_page_number = None
        data_to_show = paginator.page(pagenumber).object_list

        return JsonResponse(
            {
                "pagination": {
                    "previous_page": previous_page_number,
                    "is_previous_page": paginator.page(pagenumber).has_previous(),
                    "next_page": next_page_number,
                    "is_next_page": paginator.page(pagenumber).has_next(),
                    "start_index": paginator.page(pagenumber).start_index(),
                    "end_index": paginator.page(pagenumber).end_index(),
                    "total_entries": paginator.count,
                    "total_pages": paginator.num_pages,
                    "page": int(pagenumber),
                },
                "results": data_to_show,
                'status': True
            },
            safe=False,
        )

    @data_response
    def get(self, request, **kw):
        return self.same_call_for_get_and_post(request, **kw)

    @data_response
    def post(self, request, **kw):
        return self.same_call_for_get_and_post(request, **kw)
    
class AllCoursesByUser(APIView):
    """To get details of all the registered courses of users"""
    def get(self, request):
        username = request.GET.get("username")
        if not username:
            return Response(responsedata(False, "Username is required"), status=status.HTTP_400_BAD_REQUEST)

        try:
            user = UserSettings.objects.filter(userName=username).values().first()
            if not user:
                return Response(responsedata(False, "User not found"), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response(responsedata(False, str(e)), status=status.HTTP_400_BAD_REQUEST)

        qs = {"user_id": user["uuid"]}
        if request.GET.get("category"):
            qs["course_license_id__course_id__category__icontains"] = request.GET.get("category")
        course_licenses_ids = CourseLicenseUser.objects.filter(**qs).values("pk", "course_license_id", "course_completion")
        qs.pop('course_license_id__course_id__category__icontains', None)

        # Query the LabLicenseUser model and retrieve 'lab_license_id' values as a list
        lab_license_ids = list(LabLicenseUser.objects.filter(**qs).values_list("lab_license_id", flat=True))

        # Query the LabLicense model to get lab_ids based on 'lab_license_id' values
        lab_ids = list(LabLicense.objects.filter(uuid__in=lab_license_ids).values_list("lab_id", flat=True))

        # Serialize the labs
        lab_serializer = LabSerializer(Lab.objects.filter(uuid__in=lab_ids), many=True)

        labs_linked_data = LabCourseLinkedLicense.objects.filter(lab_license__in=lab_ids)

        labs_data = {}
        for row in labs_linked_data:
            for col in lab_serializer.data:
                if col['uuid'] == str(row.lab_license.uuid):
                    if str(row.course.uuid) in labs_data.keys():
                        labs_data[str(row.course.uuid)].append(col)
                    else:
                        labs_data[str(row.course.uuid)] = [col]

        courses = []
        category_list = []
        elearning_states = ElearninStates.objects.filter(user_id=user["uuid"]).values('course_title', 'self_learning_completion')
        elearning_dict = {state['course_title']: state['self_learning_completion'] for state in elearning_states}

        for course_licenses_id in course_licenses_ids:
            try:
                course_id = (
                    CourseLicense.objects.filter(uuid=course_licenses_id["course_license_id"]).values(
                        "course_id").first()["course_id"]
                )
                course_name = Course.objects.filter(uuid=course_id).first()
                course_completion = course_licenses_id["course_completion"]

                # Override course_completion with data from ElearninStates if available
                course_completion = elearning_dict.get(course_name.name, course_completion)

                courses.append(
                    {
                        "course_id": course_id,
                        "course_name": course_name.name,
                        "course_completion": course_completion,
                        "description": description(course_name.description),
                        "provider": course_name.provider,
                        "link": course_name.link,
                        "category": course_name.category,
                        "course_id_talent_lms": course_name.course_id_talent_lms,
                        "event_url": course_name.event_course.first().url if course_name.event_course.first() else '',
                        "event_start_time": course_name.event_course.first().start_time if course_name.event_course.first() else '',
                        "event_end_time": course_name.event_course.first().end_time if course_name.event_course.first() else '',
                        "event_start_date": course_name.event_course.first().start_date if course_name.event_course.first() else '',
                        "event_end_date": course_name.event_course.first().end_date if course_name.event_course.first() else ''
                    }
                )

                if course_name.category not in category_list:
                    category_list.append(course_name.category)
            except Exception as exc:
                logger.exception(exc)
                pass

        # Arranging response category-wise
        all_category_wise = []
        for category in category_list:
            category_wise = []
            for item in courses:
                if category == item["category"]:
                    category_wise.append({"course": item, 'labs': labs_data.get(str(item['course_id']), [])})

            all_category_wise.append({"category": category, "data": category_wise})

        return Response(responsedata(True, "Courses retrieved successfully", {"courses": all_category_wise}),
                        status=status.HTTP_200_OK)