
 WARNING 2025-07-04 11:06:29,257 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-04 11:06:38,272 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-04 11:06:39,615 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-04 11:09:20,854 log 241 Bad Request: /project/userallocation/YOUR_PROJECT_UUID 


 WARNING 2025-07-04 11:43:36,669 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-04 11:43:48,705 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-04 11:43:49,659 autoreload 668 Watching for file changes with StatReloader 

