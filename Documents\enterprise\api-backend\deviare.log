
 WARNING 2025-06-30 09:54:00,424 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-06-30 09:54:17,652 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-06-30 09:54:18,446 autoreload 668 Watching for file changes with StatReloader 


 ERROR 2025-06-30 09:55:05,843 views 13646 Error in UserComposersAPIView: 'Composer' object has no attribute 'uuid' 


 ERROR 2025-06-30 09:55:05,856 log 241 Internal Server Error: /main/user-composers 


 WARNING 2025-06-30 09:58:25,976 log 241 Unauthorized: /main/user-composers 


 ERROR 2025-06-30 09:58:42,112 views 13646 Error in UserComposersAPIView: 'Composer' object has no attribute 'uuid' 


 ERROR 2025-06-30 09:58:42,114 log 241 Internal Server Error: /main/user-composers 


 INFO 2025-06-30 10:02:11,351 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-06-30 10:02:20,948 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-06-30 10:02:21,894 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-06-30 10:02:36,218 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-06-30 10:02:46,190 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-06-30 10:02:48,070 autoreload 668 Watching for file changes with StatReloader 


 ERROR 2025-06-30 10:03:13,674 views 13646 Error in UserComposersAPIView: 'Course' object has no attribute 'id' 


 ERROR 2025-06-30 10:03:13,675 log 241 Internal Server Error: /main/user-composers 


 INFO 2025-06-30 10:03:29,520 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-06-30 10:03:39,697 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-06-30 10:03:40,862 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-06-30 10:04:00,723 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 

