
 WARNING 2025-07-07 09:37:52,686 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-07 09:38:04,355 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-07 09:38:06,033 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-07 10:06:52,403 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\utils.py changed, reloading. 


 WARNING 2025-07-07 10:07:57,134 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-07 10:08:15,989 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-07 10:08:16,997 autoreload 668 Watching for file changes with StatReloader 

