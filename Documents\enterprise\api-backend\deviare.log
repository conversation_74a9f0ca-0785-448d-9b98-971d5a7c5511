
 WARNING 2025-07-01 10:11:03,792 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 10:11:18,195 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 10:11:19,033 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 10:11:50,055 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 10:13:21,153 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 10:14:57,566 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 10:15:08,865 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 10:15:11,003 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 10:22:12,632 views 13724 User <EMAIL> not enrolled in course Amazon Aurora Getting Started 


 ERROR 2025-07-01 10:22:16,798 views 13834 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 10:29:26,454 log 241 Bad Request: /main/composer/1 


 INFO 2025-07-01 10:30:18,398 views 13724 User <EMAIL> not enrolled in course  


 ERROR 2025-07-01 10:30:22,610 views 13834 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 10:31:56,336 views 13724 User <EMAIL> not enrolled in course  


 ERROR 2025-07-01 10:32:10,396 views 13834 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 10:42:24,494 views 13724 User <EMAIL> not enrolled in course  


 INFO 2025-07-01 10:42:37,384 views 13724 User <EMAIL> not enrolled in course AWS Course I 


 ERROR 2025-07-01 10:42:42,415 views 13834 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 10:57:36,328 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 10:57:43,075 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 10:57:43,866 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 10:59:00,192 views 13695 User <EMAIL> not enrolled in Revinova course  


 INFO 2025-07-01 10:59:11,587 views 13695 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 10:59:15,754 views 13847 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 11:01:33,258 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-01 11:01:42,457 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 11:01:43,706 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 11:02:18,572 views 13686 Processing Revinova course:  (UUID: 15b3a07f-fb49-456d-9e5f-2780bc043848) 


 INFO 2025-07-01 11:02:18,572 views 13687 Course link: https://sandbox.contentraven.com/awspartners/deviare/DirectLaunch?cid=T730BiwsEps_&io=X+NLQTe8c7A_ 


 INFO 2025-07-01 11:02:18,917 views 13694 Found 0 enrollment records <NAME_EMAIL> 


 WARNING 2025-07-01 11:02:19,245 views 13702 User <EMAIL> not enrolled in Revinova course  


 INFO 2025-07-01 11:02:30,035 views 13686 Processing Revinova course: AWS Course I (UUID: 9ce1c999-2811-4251-a10f-6ab0cde163de) 


 INFO 2025-07-01 11:02:30,035 views 13687 Course link: https://sandbox.contentraven.com/awspartners/deviare/DirectLaunch?cid=GCDY4Oh6zNY_&io=X+NLQTe8c7A_ 


 INFO 2025-07-01 11:02:30,435 views 13694 Found 0 enrollment records <NAME_EMAIL> 


 WARNING 2025-07-01 11:02:30,820 views 13702 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 11:02:34,907 views 13854 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 11:14:12,418 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 11:14:19,423 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 11:14:20,386 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 11:14:44,684 views 13696 User <EMAIL> not enrolled in Revinova course  


 INFO 2025-07-01 11:14:55,337 views 13696 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 11:14:59,474 views 13848 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 11:19:41,742 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-01 11:19:51,165 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 11:19:52,326 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 11:21:52,090 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 11:22:01,510 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 11:22:02,963 autoreload 668 Watching for file changes with StatReloader 


 ERROR 2025-07-01 11:22:36,348 views 13865 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 11:33:18,418 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 11:33:27,796 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 11:33:28,697 autoreload 668 Watching for file changes with StatReloader 


 ERROR 2025-07-01 11:34:07,662 views 13865 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 11:41:02,868 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 11:41:10,403 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 11:41:11,263 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 11:41:35,796 views 13699 User <EMAIL> not enrolled in Revinova course  


 INFO 2025-07-01 11:41:46,666 views 13699 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 11:41:52,051 views 13851 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 11:52:33,306 views 13699 User <EMAIL> not enrolled in Revinova course  


 INFO 2025-07-01 11:52:44,249 views 13699 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 11:52:49,834 views 13851 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 11:56:11,132 log 241 Bad Request: /main/composer-assign 


 INFO 2025-07-01 11:57:43,825 views 13699 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 11:57:47,764 views 13851 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 11:58:56,861 views 13699 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 11:59:02,271 views 13851 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 12:04:42,252 views 13699 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 12:04:47,451 views 13851 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 12:13:12,528 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 12:13:18,311 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 12:13:18,980 autoreload 668 Watching for file changes with StatReloader 


 ERROR 2025-07-01 12:14:07,392 views 13865 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 ERROR 2025-07-01 12:15:17,390 views 13865 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 ERROR 2025-07-01 12:16:31,930 views 13865 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 12:20:34,384 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-01 12:20:44,594 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 12:20:45,787 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 12:22:39,281 views 13701 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 12:22:45,324 views 13853 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 12:34:07,731 log 241 Bad Request: /main/composer-assign 


 INFO 2025-07-01 12:35:21,618 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 

