#!/usr/bin/env python
"""
Test the API after adding the Revinova course to the composer
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import Course, CourseLicenseUser, UserSettings, Composer
from main.views import UserComposersAPIView
from django.test import RequestFactory
from django.contrib.auth.models import AnonymousUser

class MockUser:
    def __init__(self, email):
        self.email = email

def test_api():
    print("=== TESTING API AFTER FIX ===")
    
    # Create a mock request
    factory = RequestFactory()
    request = factory.get('/user-composers')
    request.user = MockUser('<EMAIL>')
    
    # Create the view and call it
    view = UserComposersAPIView()
    
    try:
        response = view.get(request)
        print(f"Status Code: {response.status_code}")
        print("Response Data:")
        
        import json
        data = response.data
        print(json.dumps(data, indent=2))
        
        # Check specifically for Revinova courses
        print("\n=== REVINOVA COURSES IN RESPONSE ===")
        for group in data.get('groups', []):
            for item in group.get('items', []):
                if item.get('type') == 'course' and 'revinova' in item.get('provider', '').lower():
                    print(f"✅ Found Revinova course:")
                    print(f"   Name: {item.get('name')}")
                    print(f"   UUID: {item.get('uuid')}")
                    print(f"   Launch URL: {item.get('launch_url')}")
                    print()
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api()
