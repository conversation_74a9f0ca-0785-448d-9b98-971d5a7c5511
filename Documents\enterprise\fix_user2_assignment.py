#!/usr/bin/env python
"""
Fix User 2 assignment - remove them from Composer 1, keep only Composer 6
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import UserSettings, ComposerLicenseUser, ComposerLicense

def fix_user2_assignment():
    print("🔧 Fixing User 2 Composer Assignment")
    print("=" * 50)
    
    try:
        # Get the user
        user = UserSettings.objects.get(email="<EMAIL>")
        print(f"Found user: {user.email}")
        
        # Show current assignments
        current_assignments = ComposerLicenseUser.objects.filter(user_id=user)
        print(f"\nCurrent assignments: {current_assignments.count()}")
        for assignment in current_assignments:
            composer = assignment.composer_license_id.composer_id
            print(f"  - Composer {composer.id}: {composer.product_name}")
        
        # Remove assignment from Composer 1 (keep only Composer 6)
        composer_1_assignments = ComposerLicenseUser.objects.filter(
            user_id=user,
            composer_license_id__composer_id__id=1
        )
        
        if composer_1_assignments.exists():
            print(f"\n🗑️ Removing {composer_1_assignments.count()} assignment(s) from Composer 1...")
            composer_1_assignments.delete()
            print("✅ Removed assignment from Composer 1")
        else:
            print("\n⚠️ No assignment to Composer 1 found")
        
        # Verify final state
        final_assignments = ComposerLicenseUser.objects.filter(user_id=user)
        print(f"\nFinal assignments: {final_assignments.count()}")
        for assignment in final_assignments:
            composer = assignment.composer_license_id.composer_id
            print(f"  - Composer {composer.id}: {composer.product_name}")
        
        if final_assignments.count() == 1:
            composer = final_assignments.first().composer_license_id.composer_id
            if composer.id == 6:
                print("\n✅ SUCCESS: User now assigned only to Composer 6!")
            else:
                print(f"\n⚠️ WARNING: User assigned to Composer {composer.id}, not 6")
        else:
            print(f"\n⚠️ WARNING: User has {final_assignments.count()} assignments, expected 1")
            
    except UserSettings.DoesNotExist:
        print("❌ User <EMAIL> not found")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    fix_user2_assignment()
