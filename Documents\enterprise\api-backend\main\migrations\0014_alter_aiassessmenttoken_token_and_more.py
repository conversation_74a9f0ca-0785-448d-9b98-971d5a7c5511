# Generated by Django 4.2.20 on 2025-07-04 09:43

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0013_alter_aiassessmenttoken_token_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='aiassessmenttoken',
            name='token',
            field=models.CharField(default='3e4bb5f1d1b64b7', max_length=15, unique=True),
        ),
        migrations.AlterField(
            model_name='assessmenttoken',
            name='token',
            field=models.CharField(default='f347c902be6d436', max_length=15, unique=True),
        ),
        migrations.CreateModel(
            name='ComposerCustomer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('count', models.CharField(default='', max_length=100, null=True)),
                ('initial_count', models.<PERSON>r<PERSON><PERSON>(default='', max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('composer_customer_for', models.ForeignKey(default='', on_delete=django.db.models.deletion.CASCADE, related_name='composer_customer_for', to='main.composer')),
            ],
            options={
                'verbose_name': 'ComposerCustomers',
                'verbose_name_plural': 'ComposerCustomer',
            },
        ),
        migrations.AddField(
            model_name='assignlicensetocustomer',
            name='composer_customer',
            field=models.ManyToManyField(blank=True, related_name='composer_customer', to='main.composercustomer'),
        ),
    ]
