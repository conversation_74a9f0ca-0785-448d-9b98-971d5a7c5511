#!/usr/bin/env python
"""
Test script to verify Revinova course launch URL logic
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import Course, CourseLicenseUser, UserSettings
from main.views import UserComposersAPIView

def test_revinova_logic():
    print("=== TESTING REVINOVA LAUNCH URL LOGIC ===")
    
    # Get the AWS Course I that has enrollments
    course_uuid = "9ce1c999-2811-4251-a10f-6ab0cde163de"
    course = Course.objects.get(uuid=course_uuid)
    
    print(f"Testing course: {course.name} (UUID: {course_uuid})")
    print(f"Course link: {course.link}")
    
    # Test with enrolled user
    enrolled_email = "<EMAIL>"
    try:
        enrolled_user = UserSettings.objects.get(email=enrolled_email)
        print(f"\nTesting with enrolled user: {enrolled_email}")
        
        # Check enrollment
        enrollment = CourseLicenseUser.objects.filter(
            user_id=enrolled_user,
            course_license_id__course_id=course
        )
        print(f"Enrollment exists: {enrollment.exists()}")
        
        if enrollment.exists():
            print(f"Expected result: {course.link}")
            print("✅ This user should get the launch URL")
        else:
            print("❌ This user should NOT get the launch URL")
            
    except UserSettings.DoesNotExist:
        print(f"User {enrolled_email} not found")
    
    # Test with non-enrolled user
    non_enrolled_email = "<EMAIL>"
    try:
        non_enrolled_user = UserSettings.objects.get(email=non_enrolled_email)
        print(f"\nTesting with non-enrolled user: {non_enrolled_email}")
        
        # Check enrollment
        enrollment = CourseLicenseUser.objects.filter(
            user_id=non_enrolled_user,
            course_license_id__course_id=course
        )
        print(f"Enrollment exists: {enrollment.exists()}")
        
        if enrollment.exists():
            print(f"Expected result: {course.link}")
            print("✅ This user should get the launch URL")
        else:
            print("Expected result: null")
            print("❌ This user should NOT get the launch URL (correct behavior)")
            
    except UserSettings.DoesNotExist:
        print(f"User {non_enrolled_email} not found")

if __name__ == "__main__":
    test_revinova_logic()
