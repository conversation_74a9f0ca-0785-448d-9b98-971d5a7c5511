# Generated by Django 4.2.20 on 2025-07-01 12:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0008_remove_composer_sync_frequency_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='composer',
            name='is_sequence',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='aiassessmenttoken',
            name='token',
            field=models.CharField(default='ecda2b8e7b3d43e', max_length=15, unique=True),
        ),
        migrations.AlterField(
            model_name='assessmenttoken',
            name='token',
            field=models.CharField(default='837eb2c7ffd6416', max_length=15, unique=True),
        ),
        migrations.CreateModel(
            name='ComposerCourse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sequence_number', models.PositiveIntegerField(default=0)),
                ('composer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='main.composer')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='main.course')),
            ],
        ),
        migrations.CreateModel(
            name='ComposerAssessment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sequence_number', models.PositiveIntegerField(default=0)),
                ('assessment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='main.assessment')),
                ('composer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='main.composer')),
            ],
        ),
        migrations.CreateModel(
            name='ComposerApprenticeship',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sequence_number', models.PositiveIntegerField(default=0)),
                ('apprenticeship', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='main.apprenticeship')),
                ('composer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='main.composer')),
            ],
        ),
        migrations.AlterField(
            model_name='composer',
            name='apprenticeships',
            field=models.ManyToManyField(blank=True, through='main.ComposerApprenticeship', to='main.apprenticeship'),
        ),
        migrations.AlterField(
            model_name='composer',
            name='assessments',
            field=models.ManyToManyField(blank=True, through='main.ComposerAssessment', to='main.assessment'),
        ),
        migrations.AlterField(
            model_name='composer',
            name='courses',
            field=models.ManyToManyField(blank=True, through='main.ComposerCourse', to='main.course'),
        ),
    ]
