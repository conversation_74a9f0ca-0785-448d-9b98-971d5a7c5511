# Generated by Django 4.2.20 on 2025-07-01 10:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0007_remove_composer_labs_alter_aiassessmenttoken_token_and_more'),
    ]

    operations = [
        migrations.<PERSON>move<PERSON>ield(
            model_name='composer',
            name='sync_frequency',
        ),
        migrations.AlterField(
            model_name='aiassessmenttoken',
            name='token',
            field=models.CharField(default='6349f33f6e994a0', max_length=15, unique=True),
        ),
        migrations.AlterField(
            model_name='assessmenttoken',
            name='token',
            field=models.Char<PERSON>ield(default='a6670e74c550469', max_length=15, unique=True),
        ),
    ]
