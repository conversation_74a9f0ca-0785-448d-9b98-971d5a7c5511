
 WARNING 2025-07-01 10:11:03,792 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 10:11:18,195 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 10:11:19,033 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 10:11:50,055 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 10:13:21,153 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 10:14:57,566 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 10:15:08,865 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 10:15:11,003 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 10:22:12,632 views 13724 User <EMAIL> not enrolled in course Amazon Aurora Getting Started 


 ERROR 2025-07-01 10:22:16,798 views 13834 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 10:29:26,454 log 241 Bad Request: /main/composer/1 


 INFO 2025-07-01 10:30:18,398 views 13724 User <EMAIL> not enrolled in course  


 ERROR 2025-07-01 10:30:22,610 views 13834 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 10:31:56,336 views 13724 User <EMAIL> not enrolled in course  


 ERROR 2025-07-01 10:32:10,396 views 13834 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 10:42:24,494 views 13724 User <EMAIL> not enrolled in course  


 INFO 2025-07-01 10:42:37,384 views 13724 User <EMAIL> not enrolled in course AWS Course I 


 ERROR 2025-07-01 10:42:42,415 views 13834 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 10:57:36,328 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 10:57:43,075 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 10:57:43,866 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 10:59:00,192 views 13695 User <EMAIL> not enrolled in Revinova course  


 INFO 2025-07-01 10:59:11,587 views 13695 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 10:59:15,754 views 13847 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 11:01:33,258 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-01 11:01:42,457 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 11:01:43,706 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 11:02:18,572 views 13686 Processing Revinova course:  (UUID: 15b3a07f-fb49-456d-9e5f-2780bc043848) 


 INFO 2025-07-01 11:02:18,572 views 13687 Course link: https://sandbox.contentraven.com/awspartners/deviare/DirectLaunch?cid=T730BiwsEps_&io=X+NLQTe8c7A_ 


 INFO 2025-07-01 11:02:18,917 views 13694 Found 0 enrollment records <NAME_EMAIL> 


 WARNING 2025-07-01 11:02:19,245 views 13702 User <EMAIL> not enrolled in Revinova course  


 INFO 2025-07-01 11:02:30,035 views 13686 Processing Revinova course: AWS Course I (UUID: 9ce1c999-2811-4251-a10f-6ab0cde163de) 


 INFO 2025-07-01 11:02:30,035 views 13687 Course link: https://sandbox.contentraven.com/awspartners/deviare/DirectLaunch?cid=GCDY4Oh6zNY_&io=X+NLQTe8c7A_ 


 INFO 2025-07-01 11:02:30,435 views 13694 Found 0 enrollment records <NAME_EMAIL> 


 WARNING 2025-07-01 11:02:30,820 views 13702 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 11:02:34,907 views 13854 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 11:14:12,418 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 11:14:19,423 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 11:14:20,386 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 11:14:44,684 views 13696 User <EMAIL> not enrolled in Revinova course  


 INFO 2025-07-01 11:14:55,337 views 13696 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 11:14:59,474 views 13848 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 11:19:41,742 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-01 11:19:51,165 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 11:19:52,326 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 11:21:52,090 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 11:22:01,510 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 11:22:02,963 autoreload 668 Watching for file changes with StatReloader 


 ERROR 2025-07-01 11:22:36,348 views 13865 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 11:33:18,418 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 11:33:27,796 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 11:33:28,697 autoreload 668 Watching for file changes with StatReloader 


 ERROR 2025-07-01 11:34:07,662 views 13865 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 11:41:02,868 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 11:41:10,403 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 11:41:11,263 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 11:41:35,796 views 13699 User <EMAIL> not enrolled in Revinova course  


 INFO 2025-07-01 11:41:46,666 views 13699 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 11:41:52,051 views 13851 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 11:52:33,306 views 13699 User <EMAIL> not enrolled in Revinova course  


 INFO 2025-07-01 11:52:44,249 views 13699 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 11:52:49,834 views 13851 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 11:56:11,132 log 241 Bad Request: /main/composer-assign 


 INFO 2025-07-01 11:57:43,825 views 13699 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 11:57:47,764 views 13851 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 11:58:56,861 views 13699 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 11:59:02,271 views 13851 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 12:04:42,252 views 13699 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 12:04:47,451 views 13851 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 12:13:12,528 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 12:13:18,311 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 12:13:18,980 autoreload 668 Watching for file changes with StatReloader 


 ERROR 2025-07-01 12:14:07,392 views 13865 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 ERROR 2025-07-01 12:15:17,390 views 13865 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 ERROR 2025-07-01 12:16:31,930 views 13865 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 12:20:34,384 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-01 12:20:44,594 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 12:20:45,787 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 12:22:39,281 views 13701 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 12:22:45,324 views 13853 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 WARNING 2025-07-01 12:34:07,731 log 241 Bad Request: /main/composer-assign 


 INFO 2025-07-01 12:35:21,618 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-01 12:35:32,222 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 12:35:33,337 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 12:35:42,435 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-01 12:35:52,576 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 12:35:54,308 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 12:36:58,857 views 13753 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 12:37:05,660 views 13905 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 12:37:53,577 views 13753 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 12:37:59,831 views 13905 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 12:38:29,951 views 13753 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 12:38:36,253 views 13905 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 12:39:29,083 views 13753 User <EMAIL> not enrolled in Revinova course AWS Course I 


 ERROR 2025-07-01 12:39:35,342 views 13905 Error getting apprenticeship launch URL for e6787ccd-f8cb-4a87-805d-de586f925dc0: 'Request' object has no attribute 'userAuth' 


 INFO 2025-07-01 12:43:40,558 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-01 12:43:50,114 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 12:43:51,024 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 12:44:22,505 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-01 12:44:31,700 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 12:44:32,697 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 12:46:34,304 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-01 12:46:45,188 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 12:46:46,348 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 12:47:20,623 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-01 12:47:30,713 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 12:47:31,740 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 12:52:22,247 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 12:52:30,302 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 12:52:31,218 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 14:07:32,834 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 14:07:42,575 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 14:07:44,051 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 14:08:32,316 log 241 Bad Request: /main/composer-assign 


 ERROR 2025-07-01 14:09:36,153 views 13835 Error getting course launch URL for Talent: get() returned more than one CourseLicense -- it returned 2! 


 ERROR 2025-07-01 14:09:36,164 views 13837 Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py", line 13796, in get_course_launch_url
    course_license, _ = CourseLicense.objects.get_or_create(
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 916, in get_or_create
    return self.get(**kwargs), False
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 640, in get
    raise self.model.MultipleObjectsReturned(
main.models.CourseLicense.MultipleObjectsReturned: get() returned more than one CourseLicense -- it returned 2!
 


 INFO 2025-07-01 14:13:50,641 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\utils.py changed, reloading. 


 WARNING 2025-07-01 14:14:00,179 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 14:14:01,394 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 14:14:08,090 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\utils.py changed, reloading. 


 WARNING 2025-07-01 14:14:18,462 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 14:14:19,652 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 14:18:43,613 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 14:18:53,175 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 14:18:54,480 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 15:54:59,677 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 15:55:32,151 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 15:58:57,858 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 15:59:05,606 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 15:59:06,571 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 16:17:06,843 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 16:17:15,921 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 16:17:17,801 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 16:18:26,682 log 241 Not Found: //main/composer-dropdown 


 WARNING 2025-07-01 16:39:06,731 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 16:39:15,602 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 16:39:16,522 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 16:41:18,739 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 16:41:27,808 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 16:41:28,842 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 17:01:25,348 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 17:01:34,678 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 17:01:35,638 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 17:15:42,481 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 INFO 2025-07-01 17:15:49,483 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-01 17:16:00,262 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-01 17:16:09,627 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 17:16:10,943 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 17:16:35,248 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 17:16:44,983 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 17:16:46,346 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 18:06:36,272 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 18:07:04,935 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 18:12:49,241 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 18:13:17,264 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 18:15:11,061 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 18:16:04,131 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 18:23:40,912 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 18:25:28,507 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 18:26:00,491 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 18:27:51,999 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 18:27:59,567 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 18:28:00,544 autoreload 668 Watching for file changes with StatReloader 


 ERROR 2025-07-01 18:35:52,529 log 241 Internal Server Error: /main/composer/1 
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py", line 13676, in put
    data[field] = [x for x in data[field] if not (x in seen or seen.add(x))]
  File "C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py", line 13676, in <listcomp>
    data[field] = [x for x in data[field] if not (x in seen or seen.add(x))]
TypeError: unhashable type: 'dict'

 WARNING 2025-07-01 18:40:11,531 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 18:40:20,121 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 18:40:21,080 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 18:42:16,159 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 18:42:24,311 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 18:42:25,318 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 19:02:09,022 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 19:02:18,572 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 19:02:19,835 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 19:02:45,066 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 19:02:54,715 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 19:02:55,961 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 19:02:58,010 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 19:03:25,262 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 19:03:33,998 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 19:03:34,871 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 19:06:04,646 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 19:06:14,285 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 19:06:15,617 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 19:07:56,280 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 19:08:26,560 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 19:09:00,561 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 19:09:12,128 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 19:09:13,804 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-01 19:11:53,435 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 19:12:30,431 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 19:13:14,013 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 19:14:23,391 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-01 19:14:33,028 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-01 19:14:34,234 autoreload 668 Watching for file changes with StatReloader 

