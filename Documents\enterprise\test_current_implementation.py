#!/usr/bin/env python
"""
Test what the current implementation should return
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import Course, Assessment, Apprenticeship, CourseLicenseUser, AssessmentLicenseUser, ApprenticeshipLicenseUser, UserSettings, Composer

def test_current_implementation():
    print("=== TESTING CURRENT IMPLEMENTATION ===")
    
    # Get the test user
    user = UserSettings.objects.get(email="<EMAIL>")
    print(f"User: {user.email}")
    
    # Get all composers (like the API does)
    composers = Composer.objects.all()
    print(f"Total composers: {composers.count()}")
    
    for composer in composers:
        print(f"\n--- Composer: {composer.product_name} (ID: {composer.id}) ---")
        
        items = []
        
        # --- Courses (like the API does) ---
        print("COURSES:")
        for course in composer.courses.all():
            # Check enrollment for launch URL
            enrolled = CourseLicenseUser.objects.filter(
                user_id=user,
                course_license_id__course_id=course
            ).exists()
            
            launch_url = None
            if enrolled:
                if course.provider.lower() == "revinova":
                    launch_url = course.link
                else:
                    launch_url = "WOULD_BE_GENERATED"  # TalentLMS/Xpert would generate URL
            
            items.append({
                "type": "course",
                "uuid": str(course.uuid),
                "name": course.name,
                "provider": course.provider,
                "launch_url": launch_url
            })
            
            print(f"  ✓ {course.name} ({course.provider}) - UUID: {course.uuid}")
            print(f"    Enrolled: {enrolled}, Launch URL: {launch_url}")
        
        # --- Assessments (like the API does) ---
        print("\nASSESSMENTS:")
        for assessment in composer.assessments.all():
            # Check assignment for launch URL
            assigned = AssessmentLicenseUser.objects.filter(
                user_id=user,
                assessment_license_id__assessment_id=assessment
            ).exists()
            
            launch_url = None
            if assigned:
                launch_url = "WOULD_BE_GENERATED"  # Assessment URL would be generated
            
            items.append({
                "type": "assessment",
                "uuid": str(assessment.uuid),
                "name": assessment.name,
                "launch_url": launch_url
            })
            
            print(f"  ✓ {assessment.name} - UUID: {assessment.uuid}")
            print(f"    Assigned: {assigned}, Launch URL: {launch_url}")
        
        # --- Apprenticeships (like the API does) ---
        print("\nAPPRENTICESHIPS:")
        for apprenticeship in composer.apprenticeships.all():
            # Check assignment for launch URL
            assigned = ApprenticeshipLicenseUser.objects.filter(
                user_id=user,
                apprenticeship_license_id__apprenticeship_id=apprenticeship
            ).exists()
            
            launch_url = None
            if assigned:
                launch_url = apprenticeship.link  # Static link from apprenticeship
            
            items.append({
                "type": "apprenticeship",
                "uuid": str(apprenticeship.uuid),
                "name": apprenticeship.name,
                "launch_url": launch_url
            })
            
            print(f"  ✓ {apprenticeship.name} - UUID: {apprenticeship.uuid}")
            print(f"    Assigned: {assigned}, Launch URL: {launch_url}")
        
        print(f"\nTotal items in this composer: {len(items)}")
        print("Items:")
        for item in items:
            print(f"  - {item['type']}: {item['name']} ({item.get('provider', 'N/A')})")

if __name__ == "__main__":
    test_current_implementation()
