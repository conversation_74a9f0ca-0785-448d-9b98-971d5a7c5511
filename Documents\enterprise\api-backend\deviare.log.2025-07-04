
 WARNING 2025-07-04 11:06:29,257 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-04 11:06:38,272 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-04 11:06:39,615 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-04 11:09:20,854 log 241 Bad Request: /project/userallocation/YOUR_PROJECT_UUID 


 WARNING 2025-07-04 11:43:36,669 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-04 11:43:48,705 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-04 11:43:49,659 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-04 11:46:37,216 log 241 Bad Request: /project/userallocation/88d43362b232454b8d04b3c3b267b70d 


 INFO 2025-07-04 11:59:15,634 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-04 11:59:32,421 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-04 11:59:35,156 autoreload 668 Watching for file changes with StatReloader 


 ERROR 2025-07-04 12:20:06,444 log 241 Internal Server Error: /main/user-composers 
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\base\base.py", line 289, in ensure_connection
    self.connect()
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\utils\asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\base\base.py", line 270, in connect
    self.connection = self.get_new_connection(conn_params)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\utils\asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\mysql\base.py", line 247, in get_new_connection
    connection = Database.connect(**conn_params)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\MySQLdb\__init__.py", line 123, in Connect
    return Connection(*args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\MySQLdb\connections.py", line 185, in __init__
    super().__init__(*args, **kwargs2)
MySQLdb.OperationalError: (2005, "Unknown server host 'deviaredb-prod-new.cz1yml32kck7.af-south-1.rds.amazonaws.com' (11001)")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\rest_framework\views.py", line 497, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\rest_framework\views.py", line 414, in initial
    self.perform_authentication(request)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\rest_framework\views.py", line 324, in perform_authentication
    request.user
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\rest_framework\request.py", line 231, in user
    self._authenticate()
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\rest_framework\request.py", line 384, in _authenticate
    user_auth_tuple = authenticator.authenticate(self)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\rest_framework\authentication.py", line 196, in authenticate
    return self.authenticate_credentials(token)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\rest_framework\authentication.py", line 201, in authenticate_credentials
    token = model.objects.select_related('user').get(key=key)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 633, in get
    num = len(clone)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\sql\compiler.py", line 1560, in execute_sql
    cursor = self.connection.cursor()
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\utils\asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\base\base.py", line 330, in cursor
    return self._cursor()
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\base\base.py", line 306, in _cursor
    self.ensure_connection()
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\utils\asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\base\base.py", line 289, in ensure_connection
    self.connect()
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\base\base.py", line 289, in ensure_connection
    self.connect()
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\utils\asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\base\base.py", line 270, in connect
    self.connection = self.get_new_connection(conn_params)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\utils\asyncio.py", line 26, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\mysql\base.py", line 247, in get_new_connection
    connection = Database.connect(**conn_params)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\MySQLdb\__init__.py", line 123, in Connect
    return Connection(*args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\MySQLdb\connections.py", line 185, in __init__
    super().__init__(*args, **kwargs2)
django.db.utils.OperationalError: (2005, "Unknown server host 'deviaredb-prod-new.cz1yml32kck7.af-south-1.rds.amazonaws.com' (11001)")

 WARNING 2025-07-04 13:03:16,625 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-04 13:03:30,768 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-04 13:03:32,290 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-04 13:11:55,348 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-04 13:12:05,639 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-04 13:12:06,915 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-04 13:12:15,255 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-04 13:12:27,038 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-04 13:12:28,487 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-04 13:13:31,175 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-04 13:13:44,905 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-04 13:13:46,822 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-07-04 13:25:25,000 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 WARNING 2025-07-04 13:25:35,182 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-04 13:25:36,539 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-04 13:35:05,807 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-04 13:35:15,055 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-04 13:35:16,395 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-04 13:54:47,004 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-04 13:54:54,867 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-04 13:54:55,773 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-04 15:13:37,190 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-04 15:15:33,139 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-04 16:30:33,909 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 WARNING 2025-07-04 16:30:47,921 __init__ 942 Invalid or unsupported selector 'select[multiple] option,
select:not(:has(option[selected])) option:first-of-type,
select option[selected]:not(option[selected] ~ option[selected]) ', ('Unknown pseudo-class', 'has') 


 INFO 2025-07-04 16:30:49,227 autoreload 668 Watching for file changes with StatReloader 


 WARNING 2025-07-04 16:39:06,158 log 241 Bad Request: /project/composerlicenseallocation 


 WARNING 2025-07-04 16:41:05,118 log 241 Bad Request: /project/userallocation/88d43362b232454b8d04b3c3b267b70d 

