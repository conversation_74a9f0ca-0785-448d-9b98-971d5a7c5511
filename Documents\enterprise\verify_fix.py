#!/usr/bin/env python
"""
Verify the fix worked by checking composer contents and user enrollments
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import Course, CourseLicenseUser, UserSettings, Composer

def verify_fix():
    print("=== VERIFYING THE FIX ===")
    
    # Get the test user
    user = UserSettings.objects.get(email="<EMAIL>")
    
    # Get the composer
    composer = Composer.objects.get(id=1)
    print(f"Composer: {composer.product_name}")
    
    # Check enrolled courses using the same logic as the API
    enrolled_courses = composer.courses.filter(
        course_licenses__users__user_id=user
    ).distinct()
    
    print(f"\nEnrolled courses in composer: {enrolled_courses.count()}")
    
    for course in enrolled_courses:
        print(f"✅ {course.name} ({course.provider})")
        print(f"   UUID: {course.uuid}")
        print(f"   Link: {course.link}")
        
        # Check if this is the Revinova course we added
        if course.uuid == "00284ba3-26e8-4a2a-9d2a-5d1991c4554e":
            print(f"   🎯 THIS IS THE REVINOVA COURSE WE ADDED!")
        print()

if __name__ == "__main__":
    verify_fix()
