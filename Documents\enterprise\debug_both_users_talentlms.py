#!/usr/bin/env python
"""
Debug script to compare TalentLMS behavior for both users
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import UserSettings, Course, CourseLicenseUser, Composer, ComposerLicenseUser

def debug_both_users():
    print("🔍 Debugging TalentLMS Behavior for Both Users")
    print("=" * 60)
    
    users = [
        {
            "email": "<EMAIL>",
            "token": "518b8480834336686613d7e172389e80e35805c3",
            "name": "User 1"
        },
        {
            "email": "<EMAIL>", 
            "token": "0f64649910738022d4b003fe73d81d57407aef18",
            "name": "User 2"
        }
    ]
    
    for user_info in users:
        print(f"\n{'='*20} {user_info['name']} ({'='*20}")
        
        try:
            user = UserSettings.objects.get(email=user_info["email"])
            print(f"Email: {user.email}")
            print(f"TalentLMS ID: {user.user_id_talentlms}")
            print(f"Project: {user.project_id}")
            
            # Get assigned composers
            assigned_composers = ComposerLicenseUser.objects.filter(user_id=user)
            print(f"Assigned Composers: {assigned_composers.count()}")
            
            for assignment in assigned_composers:
                composer = assignment.composer_license_id.composer_id
                print(f"  - Composer: {composer.product_name} (ID: {composer.id})")
                
                # Get TalentLMS courses in this composer
                talent_courses = Course.objects.filter(
                    composercourse__composer_id=composer,
                    provider__iexact="Talent LMS"
                )
                
                print(f"    TalentLMS Courses in Composer: {talent_courses.count()}")
                
                for course in talent_courses:
                    print(f"      - Course: {course.name}")
                    print(f"        UUID: {course.uuid}")
                    print(f"        TalentLMS ID: {course.course_id_talent_lms}")
                    
                    # Check enrollments for this course
                    enrollments = CourseLicenseUser.objects.filter(
                        user_id=user,
                        course_license_id__course_id=course
                    )
                    print(f"        Enrollments: {enrollments.count()}")
                    
                    # Test launch URL generation
                    print(f"        Testing launch URL generation...")
                    
                    from main.views import UserComposersAPIView
                    from django.test import RequestFactory
                    
                    factory = RequestFactory()
                    request = factory.get('/test')
                    request.user = type('MockUser', (), {'email': user.email})()
                    
                    view = UserComposersAPIView()
                    launch_url = view.get_course_launch_url(request, course)
                    
                    if launch_url:
                        print(f"        ✅ Launch URL: {launch_url[:50]}...")
                    else:
                        print(f"        ❌ Launch URL: None")
                        
                        # Try to understand why
                        if not user.user_id_talentlms:
                            print(f"          Reason: User has no TalentLMS ID")
                        elif not course.course_id_talent_lms:
                            print(f"          Reason: Course has no TalentLMS ID")
                        else:
                            print(f"          Reason: TalentLMS API error (user deleted from platform)")
            
        except UserSettings.DoesNotExist:
            print(f"❌ User {user_info['email']} not found")
        except Exception as e:
            print(f"❌ Error processing {user_info['email']}: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    debug_both_users()
