#!/usr/bin/env python
"""
Predict what the API response should look like after the fix
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import Course, Assessment, Apprenticeship, CourseLicenseUser, AssessmentLicenseUser, ApprenticeshipLicenseUser, UserSettings, Composer

def predict_response():
    print("=== PREDICTED API RESPONSE ===")
    
    # Get the test user
    user = UserSettings.objects.get(email="<EMAIL>")
    
    # Get the composer
    composer = Composer.objects.get(id=1)
    
    print(f"Composer: {composer.product_name}")
    print("Expected items in response:")
    
    print("\n--- COURSES ---")
    for course in composer.courses.all():
        # Check if user is enrolled
        enrolled = CourseLicenseUser.objects.filter(
            user_id=user,
            course_license_id__course_id=course
        ).exists()
        
        launch_url = "ACTUAL_URL" if enrolled else "null"
        if enrolled and course.provider.lower() == "revinova":
            launch_url = course.link
        
        print(f"✓ {course.name} ({course.provider})")
        print(f"  UUID: {course.uuid}")
        print(f"  Enrolled: {enrolled}")
        print(f"  Launch URL: {launch_url}")
        print()
    
    print("--- ASSESSMENTS ---")
    for assessment in composer.assessments.all():
        # Check if user is assigned
        assigned = AssessmentLicenseUser.objects.filter(
            user_id=user,
            assessment_license_id__assessment_id=assessment
        ).exists()
        
        launch_url = "ACTUAL_URL" if assigned else "null"
        
        print(f"✓ {assessment.name}")
        print(f"  UUID: {assessment.uuid}")
        print(f"  Assigned: {assigned}")
        print(f"  Launch URL: {launch_url}")
        print()
    
    print("--- APPRENTICESHIPS ---")
    for apprenticeship in composer.apprenticeships.all():
        # Check if user is assigned
        assigned = ApprenticeshipLicenseUser.objects.filter(
            user_id=user,
            apprenticeship_license_id__apprenticeship_id=apprenticeship
        ).exists()
        
        launch_url = "ACTUAL_URL" if assigned else "null"
        
        print(f"✓ {apprenticeship.name}")
        print(f"  UUID: {apprenticeship.uuid}")
        print(f"  Assigned: {assigned}")
        print(f"  Launch URL: {launch_url}")
        print()

if __name__ == "__main__":
    predict_response()
