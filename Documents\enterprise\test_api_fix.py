#!/usr/bin/env python
"""
Test the fixed API to ensure it shows all composer items
"""
import os
import sys
import django
import requests

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import Composer

def test_api():
    print("=== TESTING FIXED API ===")
    
    # Check what's in the composer
    composer = Composer.objects.get(id=1)
    print(f"Composer: {composer.product_name}")
    
    print(f"\nCourses in composer: {composer.courses.count()}")
    for course in composer.courses.all():
        print(f"  - {course.name} ({course.provider}) - UUID: {course.uuid}")
    
    print(f"\nAssessments in composer: {composer.assessments.count()}")
    for assessment in composer.assessments.all():
        print(f"  - {assessment.name} - UUID: {assessment.uuid}")
        
    print(f"\nApprenticeships in composer: {composer.apprenticeships.count()}")
    for apprenticeship in composer.apprenticeships.all():
        print(f"  - {apprenticeship.name} - UUID: {apprenticeship.uuid}")
    
    total_items = composer.courses.count() + composer.assessments.count() + composer.apprenticeships.count()
    print(f"\nTotal items in composer: {total_items}")
    print("Expected API response should show ALL these items!")

if __name__ == "__main__":
    test_api()
