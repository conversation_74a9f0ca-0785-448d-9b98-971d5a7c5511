#!/usr/bin/env python
"""
Assign composer to user and check the results
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import UserSettings, Composer, CourseLicenseUser, AssessmentLicenseUser, ApprenticeshipLicenseUser
from main.utils import assign_composer_to_user

def assign_and_check():
    print("=== ASSIGNING COMPOSER TO USER ===")
    
    # User details
    user_id = "41e7f0529431425685e27058673c1938"
    composer_id = 1
    
    try:
        user = UserSettings.objects.get(uuid=user_id)
        print(f"User: {user.email} ({user.userName})")
        
        composer = Composer.objects.get(id=composer_id)
        print(f"Composer: {composer.product_name}")
        
        # Run the assignment
        result = assign_composer_to_user(composer_id, user_id)
        print(f"\nAssignment Result: {result}")
        
        # Check what was assigned
        print("\n=== CHECKING ASSIGNMENTS ===")
        
        print("\nCourse Assignments:")
        course_assignments = CourseLicenseUser.objects.filter(user_id=user)
        for assignment in course_assignments:
            course = assignment.course_license_id.course_id
            print(f"  ✓ {course.name} ({course.provider}) - UUID: {course.uuid}")
        
        print("\nAssessment Assignments:")
        assessment_assignments = AssessmentLicenseUser.objects.filter(user_id=user)
        for assignment in assessment_assignments:
            assessment = assignment.assessment_license_id.assessment_id
            print(f"  ✓ {assessment.name} - UUID: {assessment.uuid}")
            
        print("\nApprenticeship Assignments:")
        apprenticeship_assignments = ApprenticeshipLicenseUser.objects.filter(user=user)
        for assignment in apprenticeship_assignments:
            apprenticeship = assignment.apprenticeship_license.apprenticeship
            print(f"  ✓ {apprenticeship.name} - UUID: {apprenticeship.uuid}")
        
        # Check specifically for AWS Course I
        print("\n=== CHECKING AWS COURSE I SPECIFICALLY ===")
        aws_course_uuid = "9ce1c999-2811-4251-a10f-6ab0cde163de"
        aws_assignment = CourseLicenseUser.objects.filter(
            user_id=user,
            course_license_id__course_id__uuid=aws_course_uuid
        ).first()
        
        if aws_assignment:
            print(f"✅ AWS Course I IS assigned to user!")
            print(f"   Course: {aws_assignment.course_license_id.course_id.name}")
            print(f"   User: {aws_assignment.user_id.email}")
        else:
            print(f"❌ AWS Course I is NOT assigned to user!")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    assign_and_check()
