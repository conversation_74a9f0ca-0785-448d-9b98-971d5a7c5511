#!/usr/bin/env python
"""
Debug the filtering logic for composers and user enrollments
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import Course, CourseLicenseUser, UserSettings, Composer

def debug_filtering():
    print("=== DEBUGGING COMPOSER FILTERING ===")
    
    # Get the test user
    test_email = "<EMAIL>"
    user = UserSettings.objects.get(email=test_email)
    print(f"User: {user.email}")
    
    # Get all composers
    composers = Composer.objects.all()
    print(f"Total composers: {composers.count()}")
    
    for composer in composers:
        print(f"\n--- Composer: {composer.product_name} (ID: {composer.id}) ---")
        
        # Check all courses in this composer
        all_courses = composer.courses.all()
        print(f"Total courses in composer: {all_courses.count()}")
        
        if all_courses.exists():
            print("All courses in composer:")
            for course in all_courses:
                print(f"  - {course.name} ({course.provider}) - UUID: {course.uuid}")
        
        # Test the filtering I used
        enrolled_courses = composer.courses.filter(
            course_licenses__users__user_id=user
        ).distinct()
        print(f"Enrolled courses (using my filter): {enrolled_courses.count()}")
        
        if enrolled_courses.exists():
            print("Enrolled courses:")
            for course in enrolled_courses:
                print(f"  - {course.name} ({course.provider}) - UUID: {course.uuid}")
        
        # Alternative filtering approach - check manually
        print("Manual enrollment check:")
        for course in all_courses:
            enrollment = CourseLicenseUser.objects.filter(
                user_id=user,
                course_license_id__course_id=course
            )
            if enrollment.exists():
                print(f"  ✅ {course.name} ({course.provider}) - ENROLLED")
            else:
                print(f"  ❌ {course.name} ({course.provider}) - NOT ENROLLED")

if __name__ == "__main__":
    debug_filtering()
