#!/usr/bin/env python
"""
Check enrollment status for specific course
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import Course, CourseLicenseUser, UserSettings

def check_enrollment():
    print("=== CHECKING ENROLLMENT STATUS ===")
    
    # Get the test user
    user = UserSettings.objects.get(email="<EMAIL>")
    print(f"User: {user.email}")
    
    # Check specific course
    course_uuid = "9ce1c999-2811-4251-a10f-6ab0cde163de"
    course = Course.objects.get(uuid=course_uuid)
    print(f"\nCourse: {course.name} ({course.provider})")
    print(f"Course UUID: {course.uuid}")
    print(f"Course Link: {course.link}")
    
    # Check enrollment
    enrollment = CourseLicenseUser.objects.filter(
        user_id=user,
        course_license_id__course_id=course
    )
    
    print(f"\nEnrollment check:")
    print(f"Query: CourseLicenseUser.objects.filter(user_id={user.email}, course_license_id__course_id={course.uuid})")
    print(f"Exists: {enrollment.exists()}")
    print(f"Count: {enrollment.count()}")
    
    if enrollment.exists():
        for enroll in enrollment:
            print(f"  - Enrollment UUID: {enroll.uuid}")
            print(f"  - Course License: {enroll.course_license_id}")
            print(f"  - Project: {enroll.course_license_id.project_id}")
    else:
        print("  - No enrollment found")
        
        # Check if there are any course licenses for this course
        from main.models import CourseLicense
        course_licenses = CourseLicense.objects.filter(course_id=course)
        print(f"\nAvailable course licenses for this course:")
        print(f"Count: {course_licenses.count()}")
        
        for license in course_licenses:
            print(f"  - License UUID: {license.uuid}")
            print(f"  - Project: {license.project_id}")
            print(f"  - Count: {license.count}")
            
            # Check users enrolled in this license
            users_in_license = CourseLicenseUser.objects.filter(course_license_id=license)
            print(f"  - Users enrolled: {users_in_license.count()}")
            for user_enroll in users_in_license:
                print(f"    * {user_enroll.user_id.email}")
    
    # Check all enrollments for this user
    print(f"\n=== ALL ENROLLMENTS FOR {user.email} ===")
    all_enrollments = CourseLicenseUser.objects.filter(user_id=user)
    print(f"Total enrollments: {all_enrollments.count()}")
    
    for enroll in all_enrollments:
        course_name = enroll.course_license_id.course_id.name
        course_provider = enroll.course_license_id.course_id.provider
        course_uuid = enroll.course_license_id.course_id.uuid
        print(f"  - {course_name} ({course_provider}) - UUID: {course_uuid}")

if __name__ == "__main__":
    check_enrollment()
