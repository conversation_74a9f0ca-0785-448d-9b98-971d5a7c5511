#!/usr/bin/env python
"""
Test the fixed UserComposersAPIView to ensure it only shows assigned composers
"""

import requests
import json

def test_user_composers_api():
    """Test the fixed user composers API for both users"""
    
    base_url = "http://127.0.0.1:8000/main/user-composers"
    
    # Test cases: [email, token, expected_composer_ids]
    test_cases = [
        {
            "name": "User 1 (<EMAIL>)",
            "token": "518b8480834336686613d7e172389e80e35805c3",
            "expected_composers": ["1"],  # Should only see composer 1
            "expected_names": ["My Bundle"]
        },
        {
            "name": "User 2 (<EMAIL>)",
            "token": "0f64649910738022d4b003fe73d81d57407aef18",
            "expected_composers": ["6"],  # Now assigned only to composer 6
            "expected_names": ["My Bundle 6"]
        }
    ]
    
    print("🧪 Testing Fixed UserComposersAPIView")
    print("=" * 60)
    
    for test_case in test_cases:
        print(f"\n📋 Testing: {test_case['name']}")
        print("-" * 40)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Token {test_case['token']}"
        }
        
        try:
            response = requests.get(base_url, headers=headers)
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                groups = data.get("groups", [])
                
                print(f"Number of composers returned: {len(groups)}")
                print(f"Expected composers: {test_case['expected_composers']}")
                
                returned_composer_ids = [group["uuid"] for group in groups]
                returned_composer_names = [group["product_name"] for group in groups]
                
                print(f"Returned composer IDs: {returned_composer_ids}")
                print(f"Returned composer names: {returned_composer_names}")
                
                # Check if the returned composers match expected
                if set(returned_composer_ids) == set(test_case['expected_composers']):
                    print("✅ CORRECT: User sees only assigned composers!")
                else:
                    print("❌ INCORRECT: User sees wrong composers!")
                    print(f"   Expected: {test_case['expected_composers']}")
                    print(f"   Got: {returned_composer_ids}")
                
                # Show launch URLs status for first composer
                if groups:
                    first_composer = groups[0]
                    items_with_urls = [item for item in first_composer["items"] if item.get("launch_url")]
                    items_without_urls = [item for item in first_composer["items"] if not item.get("launch_url")]
                    
                    print(f"Items with launch URLs: {len(items_with_urls)}")
                    print(f"Items without launch URLs: {len(items_without_urls)}")
                
            else:
                print(f"❌ API Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("❌ Could not connect to server. Make sure Django server is running.")
        except Exception as e:
            print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_user_composers_api()
