#!/usr/bin/env python
"""
Test script to check composer assignments and verify the fix
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import UserSettings, ComposerLicenseUser, ComposerLicense, Composer

def check_composer_assignments():
    print("=== COMPOSER ASSIGNMENTS CHECK ===")
    
    # Get all composer license users
    assignments = ComposerLicenseUser.objects.select_related(
        'user_id', 'composer_license_id__composer_id'
    ).all()
    
    print(f"Total composer assignments: {assignments.count()}")
    print()
    
    for assignment in assignments:
        user = assignment.user_id
        composer = assignment.composer_license_id.composer_id
        print(f"User: {user.email} -> Composer: {composer.id} ({composer.product_name})")
    
    print("\n=== TESTING SPECIFIC USERS ===")
    
    # Test the two users mentioned in the problem
    test_emails = [
        "<EMAIL>",  # Should only see composer 1
        "<EMAIL>"  # Should only see composer 6
    ]
    
    for email in test_emails:
        try:
            user = UserSettings.objects.get(email=email)
            print(f"\nUser: {email}")
            
            # Get assigned composers for this user
            assigned_composers = ComposerLicenseUser.objects.filter(
                user_id=user
            ).select_related('composer_license_id__composer_id')
            
            if assigned_composers.exists():
                print("Assigned to composers:")
                for assignment in assigned_composers:
                    composer = assignment.composer_license_id.composer_id
                    print(f"  - Composer {composer.id}: {composer.product_name}")
            else:
                print("  No composer assignments found!")
                
        except UserSettings.DoesNotExist:
            print(f"\nUser {email} not found in database")

if __name__ == "__main__":
    check_composer_assignments()
