# Generated by Django 4.2.20 on 2025-07-02 05:07

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0011_alter_aiassessmenttoken_token_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ComposerLicense',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('count', models.IntegerField(default=1, null=True)),
                ('initial_count', models.IntegerField(default=1, null=True)),
                ('composer_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='composer_licenses', to='main.composer')),
                ('project_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='composer_licenses', to='main.project')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AlterField(
            model_name='aiassessmenttoken',
            name='token',
            field=models.CharField(default='1c4e31ae186f4c6', max_length=15, unique=True),
        ),
        migrations.AlterField(
            model_name='assessmenttoken',
            name='token',
            field=models.CharField(default='4737b5d21205426', max_length=15, unique=True),
        ),
        migrations.CreateModel(
            name='ComposerLicenseUser',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('composer_license_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='users', to='main.composerlicense')),
                ('user_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='registered_composers', to='main.usersettings')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
