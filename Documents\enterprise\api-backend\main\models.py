from email.policy import default
# from pyexpat import model
from django.db import models
from django.contrib.auth.models import User
from django.contrib.auth.hashers import check_password, make_password
from django.dispatch import receiver
from django.db.models.signals import pre_save
from django.utils import timezone
from main.fields import Simple<PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>
# from django_mysql.models import <PERSON><PERSON><PERSON><PERSON>
import uuid
import datetime

from keycloak import keycloak_admin
from keycloak import keycloak_openid

from deviare import settings as deviare_settings
from tools.model_extra import BaseModel, DateModel
from .managers import CourseManager, ElearningStatesManager


class UserSettings(BaseModel):

    user = models.ForeignKey(User, related_name='settings', on_delete=models.CASCADE)
    reset_id = models.UUIDField(default=uuid.uuid4, editable=False)
    validated = models.BooleanField(default=False)
    user_id_talentlms = models.CharField(max_length=100, null=True, blank=True)
    firstName = models.CharField(max_length=100, null=True, blank=True)
    lastName = models.CharField(max_length=100, null=True, blank=True)
    userName = models.CharField(max_length=100, unique=True, null=True, blank=True)
    password = models.CharField(max_length=100, null=True, blank=True)
    email = models.EmailField(max_length=150, unique=True, null=True, blank=True)
    location = models.CharField(max_length=100, null=True, blank=True)
    contact_no_old = models.BigIntegerField(null=True, blank=True)
    contact_no = models.CharField(max_length=50, null=True, blank=True)
    companyID = models.ForeignKey(
        "Company",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="company",
    )
    project_id = models.ForeignKey("Project",on_delete=models.CASCADE,null=True,blank=True,related_name="project_id")
    customers = models.ManyToManyField("Company", blank=True, related_name="customers")
    country = models.CharField(max_length=100, null=True, blank=True)
    profile_image = models.URLField(null=True)
    role = models.CharField(max_length=100, null=True, blank=True)
    last_pw_update_date = models.DateTimeField(null=True, blank=True)
    last_pw_update_by = models.EmailField(max_length=150, null=True, blank=True)
    sub_role = models.CharField(max_length=100, null=True, blank=True)
    product_config = JSONField(blank=True,null=True,default=dict)
    address = models.CharField(max_length=200, null=True, blank=True)
    city = models.CharField(max_length=100, null=True, blank=True)
    pincode = models.CharField(max_length=20, null=True, blank=True)
    province = models.CharField(max_length=100, null=True, blank=True)
    skills = models.CharField(max_length=200, null=True, blank=True)
    occupation = models.CharField(max_length=100, null=True, blank=True)
    industry = models.CharField(max_length=100, null=True, blank=True)
    education_level = models.CharField(max_length=100, null=True, blank=True)
    language = models.CharField(max_length=100, null=True, blank=True)
    marketing_preferences = models.CharField(max_length=100, null=True, blank=True)
    referral_source = models.CharField(max_length=100, null=True, blank=True)
    date_of_birth = models.DateField(null=True, blank=True)

    @property
    def is_gcologist(self):
        return self.sub_role and self.sub_role == 'gcologist'

    def set_password(self, raw_password):
        self.password = make_password(raw_password)
        self._password = raw_password

    def check_password(self, raw_password):
        """
        Return a boolean of whether the raw_password was correct. Handles
        hashing formats behind the scenes.
        """

        def setter(raw_password):
            self.set_password(raw_password)
            # Password hash upgrades shouldn't be considered password changes.
            self._password = None
            self.save(update_fields=["password"])

        return check_password(raw_password, self.password, setter)

    def __str__(self):
        return self.userName

    @property
    def full_name(self):
        return "%s %s"%(self.firstName.capitalize(), self.lastName.capitalize())

    class Meta:
        db_table = "user_settings"
        verbose_name_plural = "UserSettings"

class LMSBranch(BaseModel):
    link = models.URLField(null=True)
    api_key = models.CharField(max_length=65, null=True)
    timezone = models.CharField(max_length=255, null=True, blank=True)
    name = models.CharField(max_length=150, default='', blank=True)
    lmsbranch_id = models.CharField(max_length=10, null=True, blank=True )

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Branch"
        verbose_name = "Branches"

class NotificationTemplate(BaseModel):
    name = models.CharField(max_length=150, default='', blank=True)
    from_user = models.CharField(max_length=150, default='', blank=True)
    sms_type = models.CharField(max_length=150, default='', blank=True)
    body = models.CharField(max_length=150, default='', blank=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Notificationtemplate"
        verbose_name = "Notificationtemplates"

class Instructor(BaseModel):
    name = models.CharField(max_length=150, default='', blank=True)
    image = models.URLField(null=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Instructor"
        verbose_name = "Instructors"

class Product(BaseModel):
    product_category = models.CharField(max_length=150, default='', blank=True)
    name = models.CharField(max_length=250, default='', blank=True)
    image = models.URLField(null=True)
    description = models.CharField(max_length=1000, default='', blank=True)
    price = models.FloatField(default=0, null=True)
    rating = models.IntegerField(default=0, null=True)
    benifits = models.CharField(max_length=1000, default='', blank=True)
    vga_domain = models.CharField(max_length=150, default='', blank=True)
    host_company = models.CharField(max_length=150, default='', blank=True)
    vga_schadule = models.CharField(max_length=150, default='', blank=True)
    gcologist = models.CharField(max_length=150, default='', blank=True)
    host_company_country = models.CharField(max_length=1000, default='', blank=True)
    course_type = models.CharField(max_length=150, default='', blank=True)
    course_overview = models.CharField(max_length=150, default='', blank=True)
    accreditation = models.CharField(max_length=150, default='', blank=True)
    key_features = models.CharField(max_length=1000, default='', blank=True)
    class_schedule = models.CharField(max_length=150, default='', blank=True)
    skill_covered = models.CharField(max_length=1000, default='', blank=True)
    eligibility = models.CharField(max_length=150, default='', blank=True)
    pre_requisites = models.CharField(max_length=150, default='', blank=True)
    vlab_duration = models.FloatField(default=0, null=True)
    capacity = models.IntegerField(default=0, null=True)
    instructor = models.CharField(max_length=150, default='', blank=True)
    intro_vedio = models.URLField(null=True)
    notification_forget_password_email = models.ForeignKey(NotificationTemplate,related_name='notification_forget_password_email', on_delete=models.CASCADE, null=True)
    notification_forget_password_sms = models.ForeignKey(NotificationTemplate,related_name='notification_forget_password_sms', on_delete=models.CASCADE, null=True)
    notification_register_user_email = models.ForeignKey(NotificationTemplate,related_name='notification_register_user_email', on_delete=models.CASCADE, null=True)
    notification_register_user_sms = models.ForeignKey(NotificationTemplate,related_name='notification_register_user_sms', on_delete=models.CASCADE, null=True)
    notification_signup_confirmation_email = models.ForeignKey(NotificationTemplate,related_name='notification_signup_confirmation_email', on_delete=models.CASCADE, null=True)
    notification_signup_confirmation_sms = models.ForeignKey(NotificationTemplate,related_name='notification_signup_confirmation_sms', on_delete=models.CASCADE, null=True)
    notification_application_open_notice_email = models.ForeignKey(NotificationTemplate,related_name='notification_application_open_notice_email', on_delete=models.CASCADE, null=True)
    notification_application_open_notice_sms = models.ForeignKey(NotificationTemplate,related_name='notification_application_open_notice_sms', on_delete=models.CASCADE, null=True)
    notification_application_submission_confirmation_email = models.ForeignKey(NotificationTemplate,related_name='notification_application_submission_confirmation_email', on_delete=models.CASCADE, null=True)
    notification_application_submission_confirmation_sms = models.ForeignKey(NotificationTemplate,related_name='notification_application_submission_confirmation_sms', on_delete=models.CASCADE, null=True)
    notification_assesment_request_email = models.ForeignKey(NotificationTemplate,related_name='notification_assesment_request_email', on_delete=models.CASCADE, null=True)
    notification_assesment_request_sms = models.ForeignKey(NotificationTemplate,related_name='notification_assesment_request_sms', on_delete=models.CASCADE, null=True)
    notification_assesment_reminder_email = models.ForeignKey(NotificationTemplate,related_name='notification_assesment_reminder_email', on_delete=models.CASCADE, null=True)
    notification_assesment_reminder_sms = models.ForeignKey(NotificationTemplate,related_name='notification_assesment_reminder_sms', on_delete=models.CASCADE, null=True)
    notification_backgroung_check_request_email = models.ForeignKey(NotificationTemplate,related_name='notification_backgroung_check_request_email', on_delete=models.CASCADE, null=True)
    notification_backgroung_check_request_sms = models.ForeignKey(NotificationTemplate,related_name='notification_backgroung_check_request_sms', on_delete=models.CASCADE, null=True)
    notification_application_rejection_email = models.ForeignKey(NotificationTemplate,related_name='notification_application_rejection_email', on_delete=models.CASCADE, null=True)
    notification_application_rejection_sms = models.ForeignKey(NotificationTemplate,related_name='notification_application_rejection_sms', on_delete=models.CASCADE, null=True)
    notification_application_acceptance_email = models.ForeignKey(NotificationTemplate,related_name='notification_application_acceptance_email', on_delete=models.CASCADE, null=True)
    notification_application_acceptance_sms = models.ForeignKey(NotificationTemplate,related_name='notification_application_acceptance_sms', on_delete=models.CASCADE, null=True)
    notification_application_confirmation_email = models.ForeignKey(NotificationTemplate,related_name='notification_application_confirmation_email', on_delete=models.CASCADE, null=True)
    notification_application_confirmation_sms = models.ForeignKey(NotificationTemplate,related_name='notification_application_confirmation_sms', on_delete=models.CASCADE, null=True)
    notification_signup_confirmation_talent_email = models.ForeignKey(NotificationTemplate,related_name='notification_signup_confirmation_talent_email', on_delete=models.CASCADE, null=True)
    notification_signup_confirmation_talent_sms = models.ForeignKey(NotificationTemplate,related_name='notification_signup_confirmation_talent_sms', on_delete=models.CASCADE, null=True)
    notification_signup_confirmation_for_employer_email = models.ForeignKey(NotificationTemplate,related_name='notification_signup_confirmation_for_employer_email', on_delete=models.CASCADE, null=True)
    notification_signup_confirmation_for_employer_sms = models.ForeignKey(NotificationTemplate,related_name='notification_signup_confirmation_for_employer_sms', on_delete=models.CASCADE, null=True)
    notification_password_reset_email = models.ForeignKey(NotificationTemplate,related_name='notification_password_reset_email', on_delete=models.CASCADE, null=True)
    notification_password_reset_sms = models.ForeignKey(NotificationTemplate,related_name='notification_password_reset_sms', on_delete=models.CASCADE, null=True)
    notification_export_data_email = models.ForeignKey(NotificationTemplate,related_name='notification_export_data_email', on_delete=models.CASCADE, null=True)
    notification_export_data_sms = models.ForeignKey(NotificationTemplate,related_name='notification_export_data_sms', on_delete=models.CASCADE, null=True)
    notification_interview_request_email = models.ForeignKey(NotificationTemplate,related_name='notification_interview_request_email', on_delete=models.CASCADE, null=True)
    notification_interview_request_sms = models.ForeignKey(NotificationTemplate,related_name='notification_interview_request_sms', on_delete=models.CASCADE, null=True)
    notification_interview_acceptance_email = models.ForeignKey(NotificationTemplate,related_name='notification_interview_acceptance_email', on_delete=models.CASCADE, null=True)
    notification_interview_acceptance_sms = models.ForeignKey(NotificationTemplate,related_name='notification_interview_acceptance_sms', on_delete=models.CASCADE, null=True)
    notification_interview_decline_email = models.ForeignKey(NotificationTemplate,related_name='notification_interview_decline_email', on_delete=models.CASCADE, null=True)
    notification_interview_decline_sms = models.ForeignKey(NotificationTemplate,related_name='notification_interview_decline_sms', on_delete=models.CASCADE, null=True)
    notification_relevant_talent_profile_email = models.ForeignKey(NotificationTemplate,related_name='notification_relevant_talent_profile_email', on_delete=models.CASCADE, null=True)
    notification_relevant_talent_profile_sms = models.ForeignKey(NotificationTemplate,related_name='notification_relevant_talent_profile_sms', on_delete=models.CASCADE, null=True)
    notification_profile_acceptance_decline_email = models.ForeignKey(NotificationTemplate,related_name='notification_profile_acceptance_decline_email', on_delete=models.CASCADE, null=True)
    notification_profile_acceptance_decline_sms = models.ForeignKey(NotificationTemplate,related_name='notification_profile_acceptance_decline_sms', on_delete=models.CASCADE, null=True)
    notification_job_post_email = models.ForeignKey(NotificationTemplate,related_name='notification_job_post_email', on_delete=models.CASCADE, null=True)
    notification_job_post_sms = models.ForeignKey(NotificationTemplate,related_name='notification_job_post_sms', on_delete=models.CASCADE, null=True)
    notification_profile_view_notice_email = models.ForeignKey(NotificationTemplate,related_name='notification_profile_view_notice_email', on_delete=models.CASCADE, null=True)
    notification_profile_view_notice_sms = models.ForeignKey(NotificationTemplate,related_name='notification_profile_view_notice_sms', on_delete=models.CASCADE, null=True)
    notification_job_offer_email = models.ForeignKey(NotificationTemplate,related_name='notification_job_offer_email', on_delete=models.CASCADE, null=True)
    notification_job_offer_sms = models.ForeignKey(NotificationTemplate,related_name='notification_job_offer_sms', on_delete=models.CASCADE, null=True)
    notification_job_offer_response_email = models.ForeignKey(NotificationTemplate,related_name='notification_job_offer_response_email', on_delete=models.CASCADE, null=True)
    notification_job_offer_response_sms = models.ForeignKey(NotificationTemplate,related_name='notification_job_offer_response_sms', on_delete=models.CASCADE, null=True)
    
    notification_tool_tip_page_email = models.ForeignKey(NotificationTemplate,related_name='notification_tool_tip_page_email', on_delete=models.CASCADE, null=True)
    notification_tool_tip_page_sms = models.ForeignKey(NotificationTemplate,related_name='notification_tool_tip_page_sms', on_delete=models.CASCADE, null=True)
    notification_purchese_confirmation_email = models.ForeignKey(NotificationTemplate,related_name='notification_purchese_confirmation_email', on_delete=models.CASCADE, null=True)
    notification_purchese_confirmation_sms = models.ForeignKey(NotificationTemplate,related_name='notification_purchese_confirmation_sms', on_delete=models.CASCADE, null=True)
    
    def __str__(self):
        return self.product_category

    class Meta:
        verbose_name_plural = "Product"
        verbose_name = "Products"
class ComSMPT(models.Model):
    email_host = models.CharField(max_length=200)
    email_subject_prifix = models.CharField(max_length=200)
    email_host_user = models.CharField(max_length=200)
    email_host_password = models.CharField(max_length=200)

class Company(BaseModel):
    name = models.CharField(max_length=100, unique=True)
    country = models.CharField(max_length=20, null=True, blank=True)
    address = models.CharField(max_length=255, null=True, blank=True)
    email = models.EmailField(max_length=150, null=True, blank=True)
    contact_no = models.BigIntegerField(null=True)
    logo = models.URLField(null=True)
    branch_id = models.ForeignKey(LMSBranch, related_name='branch', on_delete=models.CASCADE, null=True,blank=True)
    partner = models.CharField(max_length=90,null=True)
    domain = models.CharField(max_length=200,null=True,blank=True,unique=True)
    smtp_host = models.ForeignKey(ComSMPT, related_name="smtp_host",on_delete=models.CASCADE,null=True)
    smtp = models.BooleanField(null=True,blank=True)
    sub_domain = models.CharField(max_length=200,null=True,blank=True,unique=True)
    talent_access = models.BooleanField(default=False)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Companies"
        verbose_name = "Company"

# Customer Theme
class CustomerTheme(BaseModel):
    customer_id = models.ForeignKey(Company, related_name='customerID', on_delete=models.CASCADE)
    branch_url = models.URLField(unique=True, null=True, blank=True)
    config = JSONField(blank=True, null=True)
    sub_domain = models.CharField(blank=True, max_length=100, null=True)

    def __str__(self):
        return self.customer_id.name


# Projects
class Project(BaseModel):

    choices = (("Open", "Open"), ("Complete", "Complete"))

    project_name = models.CharField(max_length=100, null=True, blank=True)
    company_id = models.ForeignKey(Company, related_name='projects', on_delete=models.CASCADE)
    superadmin_id = models.ForeignKey(
        UserSettings, on_delete=models.CASCADE, related_name="project_created_by", null=True
    )
    company_admin_id = models.ForeignKey(
        UserSettings,
        on_delete=models.CASCADE,
        related_name="allocated_admin",
        null=True,
    )
    project_admin = models.ForeignKey(
        UserSettings,
        on_delete=models.CASCADE,
        related_name="allocated_project",
        null=True,
    )
    description = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=20, default="Open", choices=choices)
    startDate = models.DateField(auto_now_add=True, null=True)
    endDate = models.DateField(null=True, blank=True)
    is_delete = models.BooleanField(null=True,blank=True)

    def __str__(self):
        return self.project_name


class ProductState(models.Model):
    name = models.CharField(unique=True, max_length=255)

    class Meta:
        db_table = "state"

    def __str__(self):
        return '|'.join([str(self.pk), self.name])


# Courses
class Course(BaseModel):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    course_overview = models.TextField(blank=True)
    course_content = models.TextField(blank=True)
    key_features = models.TextField(blank=True)
    provider = models.CharField(max_length=30)
    link = models.CharField(max_length=200)
    category = models.CharField(max_length=100, default="Deviare Courses")
    course_id = models.CharField(max_length=30, null=True, blank=True, help_text="custom_field_1")
    course_type = models.CharField(max_length=30, null=True, blank=True, help_text="custom_field_2")
    course_id_talent_lms = models.CharField(max_length=255, null=True, blank=True, help_text="id")
    code = models.CharField(max_length=255, null=True, blank=True)
    category_id = models.CharField(max_length=10, null=True, blank=True)
    price = models.CharField(max_length=40, null=True, blank=True)
    rating = models.CharField(max_length=5, default=0, null=True, blank=True)
    status = models.CharField(max_length=255, null=True, blank=True)
    creation_date = models.CharField(max_length=255, null=True, blank=True)
    last_update_on = models.CharField(max_length=255, null=True, blank=True)
    creator_id = models.CharField(max_length=255, null=True, blank=True)
    hide_from_catalog = models.CharField(max_length=255, null=True, blank=True)
    time_limit = models.CharField(max_length=255, null=True, blank=True)
    level = models.CharField(max_length=255, null=True, blank=True)
    shared = models.CharField(max_length=255, null=True, blank=True)
    shared_url = models.CharField(max_length=255, null=True, blank=True)
    avatar = models.CharField(max_length=255, null=True, blank=True)
    big_avatar = models.CharField(max_length=255, null=True, blank=True)
    certification = models.CharField(max_length=255, null=True, blank=True)
    certification_duration = models.CharField(max_length=255, null=True, blank=True)
    e_commerce = models.BooleanField(null=True, default=None)
    project = models.BooleanField(null=True, default=None)
    live_class = models.BooleanField(null=True, default=None)
    practice_projects = models.IntegerField(null=True, default=None)
    practice_labs = models.BooleanField(null=True, default=None)
    video_url = models.CharField(max_length=255, null=True, blank=True, help_text="custom_field_3")
    accredition = models.CharField(max_length=255, null=True, blank=True, help_text="custom_field_4")
    skills_covered = models.TextField(max_length=255, null=True, blank=True, help_text="custom_field_6")
    benefits = models.TextField(max_length=255, null=True, blank=True, help_text="custom_field_9")
    eligibility = models.TextField(max_length=255, null=True, blank=True, help_text="custom_field_10")
    imageUrl = models.URLField(null=True, blank=True)
    popular = models.BooleanField(default=False)
    objects = CourseManager()

    def __str__(self):
        return self.name

    class Meta:
        unique_together = ("course_id", "course_type")


class CourseLicense(BaseModel):
    project_id = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='course_licenses')
    course_id = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='course_licenses')
    count = models.IntegerField(default=1, null=True)
    initial_count = models.IntegerField(default=1, null=True)

    def __str__(self):
        return "%s ~ %s" % (self.project_id, self.course_id.name)


class CourseUserAssignment(BaseModel):
    """
    Model represents courses users have been
    assigned to.
    """
    course = models.ForeignKey(Course, related_name="course", on_delete=models.CASCADE)
    user = models.ForeignKey(UserSettings,
        related_name="user_assignemt",
        null=True,
        blank=True,
        on_delete=models.CASCADE)

    def __str__(self):
        return "%s %s" % (self.course.name, self.user.user.email)


class CourseRequestUser(BaseModel):
    class CourseRequestChoice(models.TextChoices):
        PENDING = "pending"
        APPROVED = "approved"
        DECLINED = "declined"
    """
    Model represents courses users have been
    assigned to.
    """
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name="course_request")
    user = models.ForeignKey(UserSettings, related_name="user_course_request", on_delete=models.CASCADE)
    status = models.CharField(choices=CourseRequestChoice.choices, default=CourseRequestChoice.PENDING, max_length=20)
    comment = models.CharField(max_length=500, blank=True, null=True)
    """pending approved declined"""

    class Meta:
        unique_together = [['course', 'user']]


    def __str__(self):
        return "%s %s" % (self.course.name, self.user.user.email)


class CourseLicenseUser(BaseModel):
    course_license_id = models.ForeignKey(CourseLicense, on_delete=models.CASCADE, related_name='users')
    user_id = models.ForeignKey(UserSettings, on_delete=models.CASCADE, related_name='registered_courses')
    course_completion = models.FloatField(null=True)
    launch_url = models.URLField(null=True, max_length=500)

    def __str__(self):
        return "%s ~ %s" % (self.course_license_id.course_id.name, self.user_id.email)


def image_directory_path(instance, filename):
    return f"{instance.name.replace(' ', '_')}/im_{filename}"




class InterimDataUsermodel(models.Model):
    user = models.ForeignKey(UserSettings,default=None,on_delete=models.CASCADE,related_name="inter_user_id")
    customer = models.ForeignKey(Company,on_delete=models.CASCADE,related_name="inter_customer_id")
    project = models.ForeignKey(Project,null=True,blank=True,on_delete=models.CASCADE,related_name="inter_project_id")
    course = models.ForeignKey(Course,default=None,null=True,blank=True,on_delete=models.CASCADE,related_name="inter_course_id")
    processed = models.BooleanField(default=False)

    def __str__(self):
        return self.user
# Assessments
class Assessment(BaseModel):
    name = models.CharField(max_length=100)
    description = models.TextField()
    duration = models.CharField(max_length=30)
    link = models.CharField(max_length=200)

    def __str__(self):
        return self.name


class AssessmentLicense(BaseModel):
    project_id = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='assessment_licenses')
    assessment_id = models.ForeignKey(Assessment, on_delete=models.CASCADE, related_name='assessment_licenses')
    count = models.IntegerField(default=1, null=True)
    initial_count = models.IntegerField(default=1, null=True)

    def __str__(self):
        return "%s ~ %s"%(self.project_id.project_name, self.assessment_id.name)


class AssessmentLicenseUser(BaseModel):
    assessment_license_id = models.ForeignKey(
        AssessmentLicense, on_delete=models.CASCADE, related_name='users'
    )
    user_id = models.ForeignKey(UserSettings, null=True, on_delete=models.CASCADE,
                                related_name='registered_assessments')

    def __str__(self):
        return "%s %s " % (self.assessment_license_id, self.user_id.email)


class AssessmentToken(models.Model):
    tid = models.AutoField(primary_key=True)
    email = models.ForeignKey(
        UserSettings,
        on_delete=models.CASCADE,
        to_field='email',
        null=True,
        blank=True,
        related_name='assignments'
    )
    token = models.CharField(max_length=15, unique=True, default=uuid.uuid4().hex[:15])
    assigned = models.BooleanField(default=False)
    
    def __str__(self):
        return f"Assignment {self.tid} - {self.token}"

    class Meta:
        db_table = "assignment_tokens"
        verbose_name_plural = "AssignmentTokens"


class AIAssessmentToken(models.Model):
    tid = models.AutoField(primary_key=True)
    email = models.ForeignKey(
        UserSettings,
        on_delete=models.CASCADE,
        to_field='email',
        null=True,
        blank=True,
        related_name='aiassignments'
    )
    token = models.CharField(max_length=15, unique=True, default=uuid.uuid4().hex[:15])
    assigned = models.BooleanField(default=False)
    
    def __str__(self):
        return f"AIAssignment {self.tid} - {self.token}"

    class Meta:
        db_table = "ai_assignment_tokens"
        verbose_name_plural = "AIAssignmentTokens"

# Apprenticeships
class Apprenticeship(BaseModel):
    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    description = models.TextField(default='', null=True)
    duration = models.CharField(max_length=150, null=True)
    link = models.TextField(null=True)
    image = models.ImageField(upload_to=image_directory_path, null=True)

    def __str__(self):
        return self.name


class ApprenticeshipLicense(BaseModel):
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='apprenticeship_licenses')
    apprenticeship = models.ForeignKey(Apprenticeship, on_delete=models.CASCADE, related_name='apprenticeship_licenses')
    count = models.IntegerField(default=1, null=True)
    initial_count = models.IntegerField(default=1, null=True)

    def __str__(self):
        return "%s ~ %s" % (self.project.project_name, self.apprenticeship.name)


class ApprenticeshipDetails(BaseModel):
    company_logo = models.TextField(null=True)
    company_name = models.CharField(max_length=255, null=True)
    description = models.TextField(null=True)
    start_date = models.DateField(null=True)
    end_date = models.DateField(null=True)


class ApprenticeshipLicenseUser(BaseModel):
    apprenticeship_license = models.ForeignKey(
        ApprenticeshipLicense,
        on_delete=models.CASCADE,
        related_name='users'
    )
    user = models.ForeignKey(
        UserSettings,
        null=True,
        on_delete=models.CASCADE,
        related_name='registered_apprenticeships'
    )
    details = models.ForeignKey(
        ApprenticeshipDetails,
        null=True,
        on_delete=models.CASCADE,
        related_name='apprentices'
    )

    def __str__(self):
        return "%s %s " % (self.apprenticeship_license, self.user.email)


# Experiments
class Experiment(BaseModel):
    name = models.CharField(max_length=100)
    description = models.TextField()
    link = models.CharField(max_length=200)

    def __str__(self):
        return self.name


class ExperimentLicense(BaseModel):
    project_id = models.ForeignKey(Project, on_delete=models.CASCADE)
    experiment_id = models.ForeignKey(Experiment, on_delete=models.CASCADE)
    count = models.IntegerField()


class ExperimentLicenseUser(BaseModel):
    experiment_license_id = models.ForeignKey(
        ExperimentLicense, on_delete=models.CASCADE
    )
    user_id = models.ForeignKey(User, on_delete=models.CASCADE)


# Deployments
class Deployment(BaseModel):
    name = models.CharField(max_length=100)
    description = models.TextField()
    repository = models.CharField(max_length=200)

    def __str__(self):
        return self.name


class DeploymentLicense(BaseModel):
    project_id = models.ForeignKey(Project, on_delete=models.CASCADE)
    deployment_id = models.ForeignKey(Deployment, on_delete=models.CASCADE)
    count = models.IntegerField()


class DeploymentLicenseUser(BaseModel):
    deployment_license_id = models.ForeignKey(
        DeploymentLicense, on_delete=models.CASCADE
    )
    user_id = models.ForeignKey(User, on_delete=models.CASCADE)


# Lab
class Lab(BaseModel):
    name = models.CharField(max_length=100)
    vendor = models.CharField(max_length=100, null=True, blank=True)
    vendor_id = models.CharField(max_length=100, null=True, blank=True)
    status = models.BooleanField(default==True)
    description = models.TextField()

    def __str__(self):
        return self.name


class LabLicense(BaseModel):
    project_id = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='lab_licenses')
    lab_id = models.ForeignKey(Lab, on_delete=models.CASCADE, related_name='lab_licenses')
    count = models.IntegerField()
    initial_count = models.IntegerField(default=1, null=True)


class LabLicenseUser(BaseModel):
    lab_license_id = models.ForeignKey(
        LabLicense, on_delete=models.CASCADE, related_name='users'
    )
    user_id = models.ForeignKey(UserSettings, on_delete=models.CASCADE, related_name='registered_labs')
    lab_completion = models.FloatField(null=True)


class LabCourseLinkedLicense(BaseModel):
    lab_license = models.ForeignKey(
        Lab, on_delete=models.CASCADE, related_name='labs'
    )
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='courses')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class Theme(BaseModel):
    company = models.ForeignKey(Company, related_name='theme', on_delete=models.CASCADE)
    config = JSONField(blank=True, null=True)
    sub_domain = models.CharField(blank=True, max_length=100, null=True)


class TMForumDimension(BaseModel):
    title = models.CharField(blank=True, max_length=255, null=True)
    value = models.IntegerField(null=True)

    class Meta:
        ordering = ['value']
        db_table = "tmforum_dimension"

    def __str__(self):
        return self.title


class TMForumSubDimension(BaseModel):
    dimension = models.ForeignKey(TMForumDimension, related_name='subDimensions', on_delete=models.CASCADE)
    value = models.IntegerField(null=True)
    title = models.CharField(blank=True, max_length=255, null=True)
    description = models.TextField(blank=True, max_length=255, null=True)

    class Meta:
        ordering = ['value']
        db_table = "tmforum_sub_dimension"

    def __str__(self):
        return "%s ~ %s"%(self.dimension.title, self.title)


class TMForumCriterion(BaseModel):
    sub_dimension = models.ForeignKey(TMForumSubDimension, related_name='criteria', on_delete=models.CASCADE)
    value = models.IntegerField(null=True)
    title = models.CharField(blank=True, max_length=255, null=True)
    description = models.TextField(blank=True, max_length=255, null=True)

    class Meta:
        ordering = ['value']
        db_table = "tmforum_criterion"


class TMForumRatingDetail(BaseModel):
    criterion = models.ForeignKey(TMForumCriterion, related_name='rating_details', on_delete=models.CASCADE)
    value = models.IntegerField(null=True)
    title = models.CharField(blank=True, max_length=255, null=True)
    description = models.TextField(blank=True, max_length=255, null=True)

    class Meta:
        db_table = "tmforum_rating_detail"


class TMForumUserResponse(BaseModel):
    owner = models.ForeignKey(UserSettings, related_name='tmforum_assessment_responses', on_delete=models.CASCADE)
    criterion = models.ForeignKey(TMForumCriterion, null=True, related_name='user_responses', on_delete=models.SET_NULL)
    aspiration = models.ForeignKey(TMForumRatingDetail, null=True, related_name='user_aspiration', on_delete=models.SET_NULL)
    status_quo = models.ForeignKey(TMForumRatingDetail, null=True, related_name='user_status_quo', on_delete=models.SET_NULL)
    comment = models.TextField(blank=True, null=True)
    document = models.TextField(blank=True, null=True)

    class Meta:
        db_table = "tmforum_user_response"
        unique_together = ('owner', 'criterion')

    def auto_assign(self):
        if self.assigned.all().count() == 0:
            t = TMForumUserAssessment.objects.using('default').filter(
                owner=self.owner, sub_dimension=self.criterion.sub_dimension
            ).first()
            t.responses.add(self)

    # def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
    #     # self.auto_assign()
    #     super().save(force_insert, force_update, using, update_fields)

    def __str__(self):
        return "%s %s " % (self.owner.email, self.criterion.title)


class TMForumUserAssessment(BaseModel):
    owner = models.ForeignKey(UserSettings, related_name='tmforum_assessment_criteria', on_delete=models.CASCADE)
    sub_dimension = models.ForeignKey(TMForumSubDimension, null=True, related_name='user_assessment', on_delete=models.CASCADE)
    responses = models.ManyToManyField(TMForumUserResponse, related_name='assigned')

    class Meta:
        db_table = "tmforum_user_assessement"
        # unique_together = ('owner', 'sub_dimension')

    def auto_update(self):
        self.responses.set(TMForumUserResponse.objects.filter(
            owner=self.owner,
            criterion__in=self.sub_dimension.criteria.values_list('uuid'))
        )

    @classmethod
    def get_or_create(cls, **kw):
        qs = cls.objects.filter(**kw)
        if qs.exists():
            return qs.first(), False
        return cls(**kw), True

    # def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
    #     # self.auto_update()
    #     super().save(force_insert, force_update, using, update_fields)


class TMForumAssignedAssessment(BaseModel):
    # course_license_user = models.ForeignKey(CourseLicenseUser, null=True, related_name='tmforum_assessment',
    #                                         on_delete=models.CASCADE)
    owner = models.ForeignKey(UserSettings, related_name='tmforum_assessment', on_delete=models.CASCADE)
    company = models.ForeignKey(Company, null=True, related_name='tmforum_assessment', on_delete=models.CASCADE)
    assessment = models.ManyToManyField(TMForumUserAssessment, related_name='assigned',)

    class Meta:
        db_table = "tmforum_assigned_assessement"


class UserReport(BaseModel):
    """
    Model instance stores user reports
    """
    user_license = models.ForeignKey(CourseLicenseUser, related_name='user_report', on_delete=models.CASCADE)
    report = JSONField(blank=True, null=True)


class Role(DateModel):
    name = models.CharField(blank=True, max_length=255, null=True)
    lms = models.CharField(blank=True, max_length=255, null=True)
    human_readable = models.CharField(blank=True, max_length=255, null=True)

    class Meta:
        db_table = "role"


class MenuItem(DateModel):
    parent = models.ForeignKey('main.MenuItem', related_name='children', on_delete=models.CASCADE)
    icon = models.CharField(blank=True, max_length=255, null=True)
    title = models.CharField(blank=True, max_length=255, null=True)
    path = models.CharField(blank=True, max_length=255, null=True)
    component = models.TextField(blank=True, null=True)
    role = models.ManyToManyField(Role, related_name='menu')

    class Meta:
        db_table = "menu_item"

    @classmethod
    def for_role(cls, role_name='user'):
        return cls.objects.filter(role__name=role_name)


class GCState(models.Model):
    """
    Awaiting GCologist
    Awaiting Token
    Assessment Sent
    Assessment Completed
    Assessment Report Ready for Review
    Assessment Reviewed

    """
    name = models.CharField(unique=True, max_length=255)

    class Meta:
        db_table = "gcstate"

    def __str__(self):
        return self.name


class GCIndexAssessment(BaseModel):
    user = models.ForeignKey(UserSettings, related_name='gcindex', on_delete=models.CASCADE)
    gcologist = models.ForeignKey(UserSettings, related_name='gcindex_users', null=True, on_delete=models.CASCADE)
    state = models.ForeignKey(GCState, related_name='gcindex', null=True, on_delete=models.CASCADE)
    token = models.TextField(blank=True, null=True)
    url = models.TextField(blank=True, null=True)
    completed = models.BooleanField(default=False, null=True)
    completed_at = models.DateTimeField(null=True)
    has_report = models.BooleanField(default=False, null=True)
    sent = models.BooleanField(default=False, null=True)
    deleted = models.BooleanField(default=False, null=True)

    class Meta:
        db_table = "gcindex_assessment"

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        changes = {}
        update_fields = []
        if not self._state.adding:
            # We have an update
            cls = self.__class__
            old = cls.objects.get(pk=self.pk)
            new = self
            changed_fields = []

            for field in cls._meta.get_fields():
                field_name = field.name
                try:
                    if getattr(old, field_name) != getattr(new, field_name):
                        changed_fields.append(field_name)
                        if field_name == 'state':
                            changes[field_name] = getattr(old, field_name)
                except Exception as exc:
                    pass
            update_fields = changed_fields
        if not self.gcologist:
            self.state = GCState.objects.get(name='Awaiting GCologist')
        else:
            # if not update_fields:
            if self._state.adding:
                self.state_id = 1
            if not self.token and self.state_id == 1:
                self.state_id = 2
                if not self._state.adding:
                    update_fields.append('state')
            else:
                if 'state' not in changes.keys():
                    self.state_id = 3
                    if not self._state.adding:
                        update_fields.append('state')
            if not self._state.adding:
                if 'state' not in changes.keys():
                    if self.url and self.state_id in [2, 3]:
                        self.state_id = 4
                        update_fields.append('state')
                    if self.sent and self.state_id < 5:
                        self.state_id = 5
                        update_fields.append('state')
                    if self.completed and (self.state_id == 10 or self.state_id < 6):
                        self.state_id = 6
                        update_fields.append('state')
                    elif self.has_report:
                        if str(changes.get('state', '')) == 'Assessment Completed' or self.state_id < 7:
                            self.state_id = 7
                            update_fields.append('state')
                update_fields = list(set(update_fields))
        super().save(force_insert, force_update, using, update_fields)


class GCIndexReport(BaseModel):
    assessment = models.ForeignKey(GCIndexAssessment, related_name='report', on_delete=models.CASCADE)
    report = models.BinaryField(null=True)
    report_data = JSONField(null=True)


class GCIndexAssessmentTrack(DateModel):
    assessment = models.ForeignKey(GCIndexAssessment, related_name='track', on_delete=models.CASCADE)
    state = models.ForeignKey(GCState, related_name='track', null=True, on_delete=models.CASCADE)
    updated_fields = JSONField(null=True, default=list)

class ElearningProject(models.Model):
    name = models.CharField(max_length=200, null=True)
# Report Customization
class ReportCustomization(BaseModel):
    user_customization = models.ForeignKey("UserSettings", related_name='user_customization', on_delete=models.CASCADE)
    report_type = models.CharField(max_length=250,null=True,blank=True)
    customizations = JSONField(null=True,blank=True)

    def __str__(self):
        return self.user_customization.userName


class ElearninStates(models.Model):
    updated_at = models.CharField(max_length=100, null=True)
    user = models.ForeignKey(UserSettings, related_name='user_key', on_delete=models.CASCADE,default=None,null=True)
    # final_user = models.ForeignKey(User, related_name='user_key', on_delete=models.CASCADE,default=None,null=True)
    final_users_new = models.ForeignKey(User, related_name='user_key', on_delete=models.CASCADE,default=None,null=True)
    name = models.CharField(max_length=120,null=True,blank=True)
    email = models.EmailField()
    account_status = models.CharField(max_length=90, blank=True,null=True) # Registerd or not
    order_type = models.CharField(max_length=90, blank=True,null=True)
    activity_level = models.CharField(max_length=90,blank=True,null=True)
    team = models.ManyToManyField(ElearningProject, blank=True,related_name="team")
    course_assignment_date = models.CharField(max_length=90,null=True,blank=True)
    course_activation_date = models.CharField(max_length=90,null=True,blank=True)
    course_type = models.CharField(max_length=90, blank=True,null=True)
    course_id = models.IntegerField(null=True)
    self_learning_completion = models.FloatField(null=True)
    course_expiration_date = models.CharField(max_length=90,null=True,blank=True)
    course_title = models.CharField(max_length=500, blank=True,null=True)
    test_score = models.FloatField(null=True)# not done
    project_result = models.CharField(max_length=70, blank=True,null=True)
    course_completion_date = models.CharField(max_length=90,null=True)
    live_class_attended = models.IntegerField(null=True) #Learning days , %
    osl_score = models.FloatField(null=True) # not done
    lvc_sore = models.FloatField(null=True) # not done
    project_score = models.IntegerField(null=True)# not done
    certification_score = models.FloatField(null=True)
    concat = models.CharField(max_length=200,null=True) # email+course_id
    program = models.CharField(max_length=200,null=True) # course name
    certification_status = models.CharField(max_length=225,null=True) #from course completion date ( is date is available then certified else not Certified
    assessment = models.CharField(max_length=500,null=True)
    last_login_date  = models.CharField(max_length=200,null=True) # Last Login Date
    last_activity_on = models.CharField(max_length=200,null=True) # Last Activity On
    self_learning_time = models.CharField(max_length=200,null=True,blank=True) # Self-Learning Time
    course_access =  models.CharField(max_length=200,null=True,blank=True) #Course Access
    program_id = models.CharField(max_length=200, blank=True,null=True) # Program Id
    enrolment_cohort_id = models.CharField(max_length=200, blank=True,null=True)  # Enrolment Cohort ID
    enrolment_cohort_name = models.CharField(max_length=200, blank=True,null=True) # Enrolment Cohort Name
    current_cohort_id = models.CharField(max_length=200, blank=True,null=True) # Current Cohort ID
    current_cohort_name  = models.CharField(max_length=200, blank=True,null=True) # Current Cohort Name
    current_cohort_start_date = models.CharField(max_length=200,null=True) #  Current Cohort Start Date
    current_cohort_end_date =  models.CharField(max_length=200,null=True) # Current Cohort End Date
    cohort_enrollment_date = models.CharField(max_length=200,null=True) # Cohort Enrollment Date
    overall_classess = models.CharField(max_length=200,null=True,blank=True) # Classes Completed/Overall Classes
    mentoring_registered = models.CharField(max_length=200,null=True,blank=True,default="NA") #  Mentoring Registered
    mentoring_attended = models.CharField(max_length=200,null=True,blank=True,default="NA")
    live_classes_registered = models.CharField(max_length=200,null=True,default="NA") # Live Classess Registered
    live_sessions_attended = models.CharField(max_length=200,null=True,default="NA")
    is_email_sent = models.BooleanField(default=False)
    is_xpertskill_course = models.BooleanField(default=False)
    # objects = ElearningStatesManager()

# Notification Type
class NotificationType(models.Model):
    
    notification_type = models.CharField(max_length=200,null=True,default="NA")
    # notification_product = models.ForeignKey(Product, related_name='product_type', on_delete=models.CASCADE,default=None,null=True)
    notification_subtype = models.CharField(max_length=200,null=True,default="NA")
    notification_product = models.CharField(max_length=200,null=True,default="NA")

    def __str__(self):
        return self.notification_type

################################## Start Customer ##########################################

class SubDimentionCustomer(models.Model):
    name = models.CharField(max_length=200,null=True,default="")
    description  = models.CharField(max_length=1000,null=True,default="")
    question_type  = models.CharField(max_length=100,null=True,default="")
    criteria_1 =  models.CharField(max_length=1000,null=True,default="initiating") # Qus
    user_name = models.CharField(max_length=1000,null=True,default="")
    company_name = models.CharField(max_length=200,null=True,default="")
     # Ans

    class Meta:
        verbose_name_plural = "SubDimentionCustomer"
        verbose_name = "SubDimentionCustomer"

    def __str__(self):
        return self.name



# customer
class CustomerDRA(models.Model):
    name = models.CharField(max_length=1000,null=True,default="")
    sub_dimention = models.ManyToManyField(SubDimentionCustomer, related_name='sub_dimention',default="")

    class Meta:
        verbose_name_plural = "CustomerDRA"
        verbose_name = "CustomerDRA"

################################## End Customer ##########################################

################################## Start Strategy ##########################################
class SubDimentionStrategy(models.Model):
    name = models.CharField(max_length=200,null=True,default="")
    description  = models.CharField(max_length=1000,null=True,default="")
    question_type  = models.CharField(max_length=100,null=True,default="")
    criteria_1 =  models.CharField(max_length=1000,null=True,default="initiating") # Qus
    user_name = models.CharField(max_length=1000,null=True,default="")
    company_name = models.CharField(max_length=200,null=True,default="")

    class Meta:
        verbose_name_plural = "SubDimentionStrategy"
        verbose_name = "SubDimentionStrategy"

    def __str__(self):
        return self.name

# Strategy
class StrategyDRA(models.Model):
    name = models.CharField(max_length=1000,null=True,default="")
    sub_dimention = models.ManyToManyField(SubDimentionStrategy, related_name='sub_dimentionstrategy',default="")

    class Meta:
        verbose_name_plural = "StrategyDRA"
        verbose_name = "StrategyDRA"

################################## End Strategy ##########################################

################################## Start Technology ##########################################
class SubDimentionTechnology(models.Model):
    name = models.CharField(max_length=200,null=True,default="")
    description  = models.CharField(max_length=1000,null=True,default="")
    question_type  = models.CharField(max_length=100,null=True,default="")
    criteria_1 =  models.CharField(max_length=1000,null=True,default="initiating") # Qus
    user_name = models.CharField(max_length=1000,null=True,default="")
    company_name = models.CharField(max_length=200,null=True,default="")


    class Meta:
        verbose_name_plural = "SubDimentionTechnology"
        verbose_name = "SubDimentionTechnology"

    def __str__(self):
        return self.name

# Technology
class TechnologyDRA(models.Model):
    name = models.CharField(max_length=1000,null=True,default="")
    sub_dimention = models.ManyToManyField(SubDimentionTechnology, related_name='sub_dimentiontechnology',default="")

    class Meta:
        verbose_name_plural = "TechnologyDRA"
        verbose_name = "TechnologyDRA"

################################## End Technology ##########################################

################################## Start Operations ##########################################
class SubDimentionOperations(models.Model):
    name = models.CharField(max_length=200,null=True,default="")
    description  = models.CharField(max_length=1000,null=True,default="")
    question_type  = models.CharField(max_length=100,null=True,default="")
    criteria_1 =  models.CharField(max_length=1000,null=True,default="initiating") # Qus
    user_name = models.CharField(max_length=1000,null=True,default="")
    company_name = models.CharField(max_length=200,null=True,default="")


    class Meta:
        verbose_name_plural = "SubDimentionOperations"
        verbose_name = "SubDimentionOperations"

    def __str__(self):
        return self.name

# Operations
class OperationsDRA(models.Model):
    name = models.CharField(max_length=1000,null=True,default="")
    sub_dimention = models.ManyToManyField(SubDimentionOperations, related_name='sub_dimentionoperations',default="")

    class Meta:
        verbose_name_plural = "OperationsDRA"
        verbose_name = "OperationsDRA"

################################## End Operations ##########################################

################################## Start Culture ##########################################
class SubDimentionCulture(models.Model):
    name = models.CharField(max_length=200,null=True,default="")
    description  = models.CharField(max_length=1000,null=True,default="")
    question_type  = models.CharField(max_length=100,null=True,default="")
    criteria_1 =  models.CharField(max_length=1000,null=True,default="initiating") # Qus
    user_name = models.CharField(max_length=1000,null=True,default="")
    company_name = models.CharField(max_length=200,null=True,default="")

    class Meta:
        verbose_name_plural = "SubDimentionCulture"
        verbose_name = "SubDimentionCulture"

    def __str__(self):
        return self.name

# Culture
class CultureDRA(models.Model):
    name = models.CharField(max_length=1000,null=True,default="")
    sub_dimention = models.ManyToManyField(SubDimentionCulture, related_name='sub_dimentionculture',default="")

    class Meta:
        verbose_name_plural = "CultureDRA"
        verbose_name = "CultureDRA"

################################## End Culture ##########################################

################################## Start Data ##########################################
class SubDimentionData(models.Model):
    name = models.CharField(max_length=200,null=True,default="")
    description  = models.CharField(max_length=1000,null=True,default="")
    question_type  = models.CharField(max_length=100,null=True,default="")
    criteria_1 =  models.CharField(max_length=1000,null=True,default="initiating") # Qus
    user_name = models.CharField(max_length=1000,null=True,default="")
    company_name = models.CharField(max_length=200,null=True,default="")


    class Meta:
        verbose_name_plural = "SubDimentionData"
        verbose_name = "SubDimentionData"

    def __str__(self):
        return self.name

# Data
class DataDRA(models.Model):
    name = models.CharField(max_length=1000,null=True,default="")
    sub_dimention = models.ManyToManyField(SubDimentionData, related_name='sub_dimentionData',default="")

    class Meta:
        verbose_name_plural = "DataDRA"
        verbose_name = "DataDRA"


################################## End Data ##########################################

# DRA Type
class DRASurveySparrow(models.Model):
    user = models.ForeignKey(UserSettings, related_name='user_keydra', on_delete=models.CASCADE,default=None,null=True)
    customer = models.ForeignKey(CustomerDRA, related_name='customer_dra', on_delete=models.CASCADE,default=None,null=True)
    strategy = models.ForeignKey(StrategyDRA, related_name='strategy_dra', on_delete=models.CASCADE,default=None,null=True)
    technology = models.ForeignKey(TechnologyDRA, related_name='technology_dra', on_delete=models.CASCADE,default=None,null=True)
    operations = models.ForeignKey(OperationsDRA, related_name='operations_dra', on_delete=models.CASCADE,default=None,null=True)
    culture = models.ForeignKey(CultureDRA, related_name='culture_dra', on_delete=models.CASCADE,default=None,null=True)
    data = models.ForeignKey(DataDRA, related_name='data_dra', on_delete=models.CASCADE,default=None,null=True)
    email = models.BooleanField(default=False)
    state = models.CharField(max_length=225,null=True,blank=True,default='STARTED')


    class Meta:
        verbose_name_plural = "DRASurveySparrow"
        verbose_name = "DRASurveySparrows"

    def __str__(self):
        return self.user.userName


class AssesmentCustomer(models.Model):
    count = models.CharField(max_length=100,null=True,default="")
    assessment_customer_for = models.ForeignKey("Assessment", related_name='assessment_customer_for',default="",on_delete=models.CASCADE)
    initial_count = models.CharField(max_length=100,null=True,default="")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "AssesmentCustomer"
        verbose_name = "AssesmentCustomers"

    def __str__(self):
        return self.count


class ApprenticeshipCustomer(models.Model):
    count = models.CharField(max_length=100,null=True,default="")
    apprenticeship_customer_for = models.ForeignKey("Apprenticeship", related_name='apprenticeship_customer_for',default="",on_delete=models.CASCADE)
    initial_count = models.CharField(max_length=100,null=True,default="")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "ApprenticeshipCustomer"
        verbose_name = "ApprenticeshipCustomers"

    def __str__(self):
        return self.count


class LabCustomer(models.Model):
    count = models.CharField(max_length=100, null=True, default="")
    lab_customer_for = models.ForeignKey("Lab", related_name='lab_customer_for', default="", on_delete=models.CASCADE)
    initial_count = models.CharField(max_length=100, null=True, default="")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "LabCustomer"
        verbose_name = "LabCustomers"

    def __str__(self):
        return self.count


class CourseCustomer(models.Model):
    count = models.CharField(max_length=100, null=True, default="")
    course_customer_for = models.ForeignKey("Course", related_name='course_customer_for', default="", on_delete=models.CASCADE)
    initial_count = models.CharField(max_length=100, null=True, default="")
    launch_url = models.URLField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "CourseCustomer"
        verbose_name = "CourseCustomers"

    def __str__(self):
        return self.count


class AssignLicenseToCustomer(models.Model):
    customer_id = models.CharField(max_length=200,null=True,default="")
    deployment = models.CharField(max_length=1000,null=True,default="")
    assessment_customer = models.ManyToManyField(AssesmentCustomer, blank=True, related_name="assessment_customer")
    course_customer = models.ManyToManyField(CourseCustomer, blank=True, related_name="course_customer")
    apprenticeship_customer = models.ManyToManyField(ApprenticeshipCustomer, blank=True, related_name="apprenticeship_customer")
    lab_customer = models.ManyToManyField(LabCustomer, blank=True, related_name="lab_customer")

    class Meta:
        verbose_name_plural = "AssignLicenseToCustomer"
        verbose_name = "AssignLicenseToCustomer"

    def __str__(self):
        return self.customer_id


# Event
class Event(BaseModel):

    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    start_time = models.TimeField(null=True, blank=True)
    end_time = models.TimeField(null=True, blank=True)
    repeat_mode = models.CharField(max_length=200,null=True,default="")
    main_type = models.CharField(max_length=30,null=True,default="")
    event_project = models.ForeignKey(Project, related_name="event_project",on_delete=models.CASCADE,null=True, default=None,blank=True)
    sub_type = models.CharField(max_length=30,null=True,default="")
    event_course = models.ForeignKey(Course, related_name="event_course", on_delete=models.CASCADE, null=True, default=None, blank=True)
    course_activity = models.CharField(max_length=500,null=True,default="")
    event_assessment = models.ForeignKey(Assessment, related_name="event_assessment", on_delete=models.CASCADE, null=True, default=None, blank=True)
    assessment_activity = models.CharField(max_length=500,null=True,default="")
    event_apprenticeship = models.ForeignKey(Apprenticeship, related_name="event_apprenticeship", on_delete=models.CASCADE, null=True, default=None, blank=True)
    apprenticeship_activity = models.CharField(max_length=500,null=True,default="")
    url = models.CharField(max_length=500,null=True,default="")
    note = models.TextField(blank=True)
    created_by = models.ForeignKey(UserSettings, on_delete=models.CASCADE, related_name="event_created_by", null=True)
    event_unique_id = models.CharField(max_length=255)
    expiry_date = models.DateField(null=True, blank=True)
    event_date_list = models.CharField(max_length=200,null=True,default="")

    def __str__(self):
        return self.name

    class Meta:
        db_table = "event"
        verbose_name = "event"
        verbose_name_plural = "event"


class OrderTransaction(BaseModel):
    transaction_id = models.CharField(max_length=100, unique=True)
    transaction_date = models.DateTimeField(null=True, blank=True)
    amount = models.DecimalField(default=0, decimal_places=2, max_digits=10)
    user = models.ForeignKey(UserSettings, on_delete=models.CASCADE, related_name="order_by", null=True)
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name="course_for", null=True, blank=True)
    name = models.CharField(max_length=100)
    email = models.EmailField(max_length=100)
    address1 = models.CharField(max_length=100, null=True, blank=True)
    address2 = models.CharField(max_length=100, null=True, blank=True)
    city = models.CharField(max_length=100, null=True, blank=True)
    state = models.CharField(max_length=100, null=True, blank=True)
    pinCode = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.DateTimeField(auto_created=True)


class SurveyResponse(models.Model):
    id = models.IntegerField(primary_key=True)
    survey_id = models.IntegerField()
    submitdate = models.DateTimeField(null=True, blank=True)
    lastpage = models.IntegerField(null=True, blank=True)
    startlanguage = models.CharField(max_length=10, null=True, blank=True)
    seed = models.CharField(max_length=50, null=True, blank=True)
    token = models.CharField(max_length=255, null=True, blank=True)
    G0Q1 = models.CharField(max_length=255, null=True, blank=True)
    G0Q2 = models.CharField(max_length=255, null=True, blank=True)
    G0Q3 = models.CharField(max_length=255, null=True, blank=True)
    G0Q4 = models.CharField(max_length=255, null=True, blank=True)
    G0Q5 = models.CharField(max_length=255, null=True, blank=True)
    G0Q6 = models.CharField(max_length=255, null=True, blank=True)
    G0Q7 = models.CharField(max_length=255, null=True, blank=True)
    G0Q8 = models.CharField(max_length=255, null=True, blank=True)
    G1Q1_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q1_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q2_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q2_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q3_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q3_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q4_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q4_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q5_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q5_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q6_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q6_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q7_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q7_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q8_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q8_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q9_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q9_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q10_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q10_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q11_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q11_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G2Q1_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G2Q1_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G2Q2_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G2Q2_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G2Q3_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G2Q3_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G2Q4_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G2Q4_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G2Q5_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G2Q5_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G02Q6_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G02Q6_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G02Q7_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G02Q7_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G02Q8_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G02Q8_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G02Q9_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G02Q9_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G02Q10_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G02Q10_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G02Q11_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G02Q11_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G3Q1_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G3Q1_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G3Q2_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G3Q2_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G3Q3_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G3Q3_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G3Q4_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G3Q4_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q1_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q1_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q2_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q2_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q3_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q3_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q4_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q4_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q5_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q5_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q6_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q6_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q7_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q7_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q8_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q8_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q9_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q9_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q10_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q10_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q1_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q1_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q2_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q2_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q3_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q3_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q4_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q4_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q5_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q5_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q6_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q6_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q7_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q7_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q8_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q8_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q9_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q9_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q10_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q10_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q11_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q11_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q12_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q12_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q13_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q13_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q14_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q14_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q15_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q15_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q16_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q16_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q17_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q17_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q18_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q18_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q19_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q19_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q20_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q20_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q21_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q21_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q22_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q22_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q23_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q23_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q24_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q24_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q25_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q25_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q26_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q26_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q27_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q27_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q28_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q28_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q29_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q29_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q30_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q30_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q31_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q31_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q32_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q32_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q33_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q33_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q1_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q1_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q2_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q2_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q3_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q3_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q4_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q4_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q5_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q5_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q6_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q6_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q7_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q7_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q8_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q8_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q9_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q9_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q10_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q10_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q11_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q11_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q12_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q12_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G7Q1 = models.TextField(null=True, blank=True)
    G7Q2 = models.TextField(null=True, blank=True)
    G7Q2_filecount = models.IntegerField(null=True, blank=True)

    class Meta:
        db_table = 'survey_responses'
        managed = True

    def __str__(self):
        return f"SurveyResponse {self.id} for Survey {self.survey_id}"
    

class AISurveyResponse(models.Model):
    id = models.IntegerField(primary_key=True)
    survey_id = models.IntegerField()
    submitdate = models.DateTimeField(null=True, blank=True)
    lastpage = models.IntegerField(null=True, blank=True)
    startlanguage = models.CharField(max_length=10, null=True, blank=True)
    seed = models.CharField(max_length=50, null=True, blank=True)
    token = models.CharField(max_length=255, null=True, blank=True)
    startdate = models.DateTimeField(null=True, blank=True)
    datestamp = models.DateTimeField(null=True, blank=True)
    G0Q1 = models.CharField(max_length=255, null=True, blank=True)
    G0Q2 = models.CharField(max_length=255, null=True, blank=True)
    G0Q3 = models.CharField(max_length=255, null=True, blank=True)
    G0Q4 = models.CharField(max_length=255, null=True, blank=True)
    G0Q5 = models.CharField(max_length=255, null=True, blank=True)
    G0Q6 = models.CharField(max_length=255, null=True, blank=True)
    G0Q7 = models.CharField(max_length=255, null=True, blank=True)
    G0Q8 = models.CharField(max_length=255, null=True, blank=True)
    # Group 1 questions
    G1Q1_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q1_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q2_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q2_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q3_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q3_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q4_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q4_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q5_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q5_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q6_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q6_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q7_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q7_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q8_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q8_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G1Q9_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G1Q9_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    # Group 2 questions
    G2Q1_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G2Q1_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G2Q2_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G2Q2_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G2Q3_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G2Q3_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G2Q4_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G2Q4_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G2Q5_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G2Q5_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    # Group 02 questions
    G02Q6_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G02Q6_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G02Q7_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G02Q7_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G02Q8_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G02Q8_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G02Q9_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G02Q9_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    # Group 3 questions
    G3Q1_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G3Q1_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G3Q2_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G3Q2_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G3Q3_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G3Q3_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G3Q4_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G3Q4_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G3Q5_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G3Q5_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G3Q6_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G3Q6_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G3Q7_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G3Q7_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G3Q8_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G3Q8_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G3Q9_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G3Q9_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    # Group 4 questions
    G4Q1_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q1_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q2_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q2_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q3_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q3_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q4_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q4_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q5_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q5_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q6_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q6_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q7_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q7_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q8_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q8_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G4Q9_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G4Q9_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    # Group 5 questions
    G5Q1_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q1_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q2_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q2_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q3_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q3_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q4_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q4_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q5_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q5_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q6_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q6_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q7_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q7_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q8_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q8_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G5Q9_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G5Q9_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    # Group 6 questions
    G6Q1_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q1_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q2_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q2_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q3_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q3_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q4_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q4_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q5_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q5_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q6_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q6_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q7_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q7_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q8_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q8_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    G6Q9_SQ1 = models.CharField(max_length=50, null=True, blank=True)
    G6Q9_SQ2 = models.CharField(max_length=50, null=True, blank=True)
    # Group 7 questions
    G7Q1 = models.TextField(null=True, blank=True)
    G7Q1_filecount = models.IntegerField(null=True, blank=True)

    class Meta:
        db_table = 'ai_survey_responses'
        managed = True

    def __str__(self):
        return f"AISurveyResponse {self.id} for Survey {self.survey_id}"


# class Composer(models.Model):
#     product_name = models.CharField(max_length=255)
#     category = models.CharField(max_length=50, choices=[('course', 'Course'), ('assessment', 'Assessment'), ('apprenticeship', 'Apprenticeship')])
#     provider = models.CharField(max_length=100, blank=True, null=True)

#     # ManyToMany to each type, can be empty if not included in this composer
#     courses = models.ManyToManyField('Course', blank=True)
#     assessments = models.ManyToManyField('Assessment', blank=True)
#     apprenticeships = models.ManyToManyField('Apprenticeship', blank=True)
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

#     def __str__(self):
#         return self.product_name
    
#     class Meta:
#         verbose_name_plural = "Composer"
#         verbose_name = "Composers"


class Composer(models.Model):
    product_name = models.CharField(max_length=255)
    category = models.CharField(
        max_length=50,
        choices=[
            ('course', 'Course'),
            ('assessment', 'Assessment'),
            ('apprenticeship', 'Apprenticeship'),
            ('lab', 'Lab')
        ]
    )
    provider = models.CharField(max_length=100, blank=True, null=True)
    is_sequence = models.BooleanField(default=False)  # <-- Add this flag

    # Use through tables to store order
    courses = models.ManyToManyField('Course', blank=True, through='ComposerCourse')
    assessments = models.ManyToManyField('Assessment', blank=True, through='ComposerAssessment')
    apprenticeships = models.ManyToManyField('Apprenticeship', blank=True, through='ComposerApprenticeship')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.product_name

    class Meta:
        verbose_name_plural = "Composer"
        verbose_name = "Composers"

# Through tables for ordering
class ComposerCourse(models.Model):
    composer = models.ForeignKey(Composer, on_delete=models.CASCADE)
    course = models.ForeignKey('Course', on_delete=models.CASCADE)
    sequence_number = models.PositiveIntegerField(default=0)  # Sequence/order

class ComposerAssessment(models.Model):
    composer = models.ForeignKey(Composer, on_delete=models.CASCADE)
    assessment = models.ForeignKey('Assessment', on_delete=models.CASCADE)
    sequence_number = models.PositiveIntegerField(default=0)

class ComposerApprenticeship(models.Model):
    composer = models.ForeignKey(Composer, on_delete=models.CASCADE)
    apprenticeship = models.ForeignKey('Apprenticeship', on_delete=models.CASCADE)
    sequence_number = models.PositiveIntegerField(default=0)


# Composer Licensing System (follows same pattern as Course/Assessment/Apprenticeship licensing)
class ComposerLicense(BaseModel):
    project_id = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='composer_licenses')
    composer_id = models.ForeignKey(Composer, on_delete=models.CASCADE, related_name='composer_licenses')
    count = models.IntegerField(default=1, null=True)
    initial_count = models.IntegerField(default=1, null=True)

    def __str__(self):
        return "%s ~ %s" % (self.project_id.project_name, self.composer_id.product_name)


class ComposerLicenseUser(BaseModel):
    composer_license_id = models.ForeignKey(ComposerLicense, on_delete=models.CASCADE, related_name='users')
    user_id = models.ForeignKey(UserSettings, on_delete=models.CASCADE, related_name='registered_composers')

    def __str__(self):
        return "%s ~ %s" % (self.composer_license_id.composer_id.product_name, self.user_id.email)