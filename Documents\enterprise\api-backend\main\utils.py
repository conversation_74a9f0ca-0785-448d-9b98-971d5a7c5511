import base64
import random
import string
import requests
import logging
from .models import UserSettings, Course
from requests.auth import HTT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from imaplib import IMAP4_SSL
from datetime import datetime, timedelta
import email
import re
import pandas as pd
import numpy as np
from hashlib import md5
from collections import OrderedDict
from deviare import settings as deviare_settings


logger = logging.getLogger(__name__)

# TODO move to environment variables
API_KEY = deviare_settings.TLMS_API_KEY
URL_ADDRESS = f"{deviare_settings.TLMS_URL_ADDRESS}/api/v1"
# secret_key = 'secret' # Make sure
secret_key = 'T9Doe2QZ0S25'


def calculate_md5(data):
    checksum = ''
    for k,v in data.items(): 
        checksum = '%s%s' % (checksum,v)
    checksum = '%s%s' % (checksum, secret_key) # add key to checksum value
    md5_hash = md5(checksum.encode('utf-8')).hexdigest()
    return md5_hash

def validate_response(response, check=True): 

    dict_ = OrderedDict()
    new = response.text.split('&')
    for item in new:
        list_ = item.split('=')
        key = list_[0]
        value = list_[1]
        dict_[key] = value
    
    if check:
        is_equal, dict_['CHECKSUM'] = validate_checksum(dict_)

        return is_equal, dict_
    else:
        return True, dict_


def validate_checksum(data):
    hash_ = data.pop('CHECKSUM')
    new_hash = calculate_md5(data)
    return hash_ == new_hash, new_hash

def email_check(email):
    email = email.casefold()
    return True if UserSettings.objects.filter(email=email).exists() else False


def randomString(stringLength=8):
    characters = [c for c in list(list(string.ascii_letters) + list(string.digits))]
    return "".join([random.choice(characters) for i in range(stringLength)])


def return_image_extenstion(base64string):
    image_splits = base64string.split(";base64,")
    extension = image_splits[0].split("/").pop()
    data = base64.b64decode(image_splits[1])
    return data, extension


def sl_cron_job():

    from main.models import Course
    from main.serializers import CourseSerializer

    # Simplilearn API for course catalog
    courses_url = "https://services.simplilearn.com/enterprise-catalog/get-course-catalog?country_code=US"
    headers = {"Authorization": "Bearer 5c166aee943ce89c741ccd8e6b8400e5"}
    all_courses = requests.get(courses_url, headers=headers)
    data = all_courses.json().get("data")
    count = 0

    # Saving into database
    for course in data:
        count += 1
        print(count)

        # Setting Blanks to Master Programs
        if course["category"] == "":
            course["category"] = "Master Programs"

        course_data = {
            "name": course["course_name"],
            "description": course["description"],
            "provider": "SimpliLearn",
            "link": course["course_url"],
            "category": course["category"],
            "course_id": course["course_id"],
            "course_type": course["course_type"],
        }

        # Update
        if Course.objects.filter(course_id=course["course_id"], course_type=course["course_type"]).exists():
            course = Course.objects.filter(course_id=course["course_id"], course_type=course["course_type"]).first()
            serializer = CourseSerializer(course, data=course_data, partial=True)
            if serializer.is_valid(raise_exception=True):
                serializer.save()
        # Save
        else:
            serializer = CourseSerializer(data=course_data)
            if serializer.is_valid(raise_exception=True):
                serializer.save()

    print("Done")


def lookup_create():
    return {lookup_key(k): v for k, v in Course.objects.filter(course_id_talent_lms__isnull=True).values_list('name', 'category')}


HT = re.compile(r'<(.*?)>')
REG_NON_WORD = re.compile(r'(\s+|\W+)', re.I)
RAZ = re.compile(r'(azure|Cloud Computing)', re.I)


def clean_name(s):    
    return HT.sub('', s).encode('latin').replace(b'\xc3', b'').replace(b'\x82', b'').replace(b'\xc2', b' ').decode('latin')


def lookup_key(s):
    return REG_NON_WORD.sub('_', s.lower())


def bulk_upsert(courses, categories):
    """
    Insert / Update course content from TalentLMS
    """
    try:
        course_id_talent_lms_list = []
        for course in courses:
            course_id_talent_lms = course.pop('id')
            exist = Course.objects.filter(course_id_talent_lms=course_id_talent_lms).first()
            if exist and exist.e_commerce:
                continue
            category = exist.category if exist else None
            course["course_id_talent_lms"] = course_id_talent_lms
            course_id_talent_lms_list.append(course_id_talent_lms)
            course["category"] = category or categories.get(course["category_id"], "Misc")
            course["provider"] = "Talent LMS"
            course['course_id'] = course.pop('custom_field_1', None)
            course['course_type'] = course.pop('custom_field_2', None)
            course['video_url'] = course.pop('custom_field_3', None)
            course['accredition'] = course.pop('custom_field_4', None)
            course['skills_covered'] = course.pop('custom_field_6', None)
            course['benefits'] = course.pop('custom_field_9', None)
            course['eligibility'] = course.pop('custom_field_10', None)
            e_com = course.pop('custom_field_13', None)
            # if e_com == "Yes":
            #     e_com = True
            # elif e_com == "No":
            #     e_com = False
            course['e_commerce'] = exist.e_commerce if exist else False
            rem_list = ['custom_field_11', 'custom_field_14', 'custom_field_12', 'custom_field_5', 'custom_field_7', 'custom_field_8', 'expiration_datetime', 'start_datetime']
            [course.pop(key) for key in rem_list if key in course.keys()]
            try:
                obj, created = Course.objects.update_or_create(course_id_talent_lms=course_id_talent_lms,category_id=course["category_id"], defaults=course)
                if created:
                    print("New course created with below details:")
                    print("Course : ",course)
                    print("course_id_talent_lms:",course_id_talent_lms)
                    #print(course_id_talent_lms_list)
            except Exception as e:
                # Log the error and move on to the next course
                logger.exception(f"Error updating/creating course with id: {course_id_talent_lms}. Error: {e}")
                print(f"Error updating/creating course with id: {course_id_talent_lms}. Skipping to the next record.")
                continue
        Course.objects.filter(provider="Talent LMS").exclude(course_id_talent_lms__in=course_id_talent_lms_list).delete()
    except Exception as exc:
        logger.exception(exc)
        print(str(exc)) 


def add_user_to_course(user_id=None, course_id=None, **kw):
    """
    Add user to course on talent LMS
    """
    try:
        payload = {"user_id": user_id, "course_id": course_id, "role": "learner"}

        url = "%s/addusertocourse" % (URL_ADDRESS)
        response = requests.request("POST", url, auth=HTTPBasicAuth(API_KEY, ""), headers={}, data=payload, files=[])

        return response

    except Exception as exc:
        logger.exception(exc)
        return None


def add_user(*args, **kwargs):
    """
    Add user to talentLMS
    """
    try:

        url = "%s/usersignup" % (URL_ADDRESS)
        response = requests.request("POST", url, auth=HTTPBasicAuth(API_KEY, ""), headers={}, data=kwargs, files=[])

        return response

    except Exception as exc:
        logger.exception(exc)
        return None


def get_talentlms_request():
    from lms.api import LmsRequestProxy
    return LmsRequestProxy()


def usersignup(user, branch_id=None):
    """
    Add user to talentLMS
    """
    try:
        if not isinstance(user, UserSettings):
            user_qs = UserSettings.objects.filter(pk=str(user))
            if user_qs.exists():
                user = user_qs.first()
        url = "usersignup"
        r = get_talentlms_request()
        # response = r.post(
        #     url,
            # data=dict(
            #     first_name=user.firstName,
            #     last_name=user.lastName,
            #     email='dnedit__'+user.email,
            #     login=user.userName,
            #     password="iceScream",
            # )
        # )
        url = URL_ADDRESS+"/usersignup"
        response = requests.request(
            "POST",
            url,
            auth=HTTPBasicAuth(API_KEY, ""),
            headers={},
            data=dict(
            first_name=user.firstName,
            last_name=user.lastName,
            email='dnedit__'+user.email,
            login=user.userName,
            password="iceScream",
            ),
            files=[],
        )
        if 'error' in response.json():
            err = response.json()['error']
            if err['message'].find('already exists') != -1:
                em = err['message'].find('already exists')
                p = f'email:{user.email}' if em else f'username:{user.userName}'
                url = f'users/{p}'
                url = URL_ADDRESS+"/"+url
                response = requests.request('GET',url,auth=HTTPBasicAuth(API_KEY, ""),headers={})
                #return user
                if response != None and 'id' in response.json():
                    user.user_id_talentlms = response.json()["id"]
                    user.save()
        if user.user_id_talentlms == None:
            url = URL_ADDRESS+"/users"
            response = requests.request('GET',url,auth=HTTPBasicAuth(API_KEY, ""),headers={})
            user_data = [i for i in eval(response.text.replace("null","1234")) if i['login'] == user.email]
            if user_data != []:
                user.user_id_talentlms = user_data[0]["id"]
                user.save()
        return user
    except Exception as exc:
        logger.exception(exc)
        return None


def parse_email(message):
    """
    Parse email body to get href link
    """
    for part in message.walk():
        if part.get_content_type() == "text/html":
            content = part.get_payload()
            hrefs = re.findall(r'href=[\'"]?([^\'" >]+)', content)

            for href in hrefs:
                if "s3.amazonaws.com" in href:
                    return href

    return None


def get_link():
    """
    IMAP outlook authentication. Retrieve email body
    from simplilearn
    """
    
    MAIL_FROM = "<EMAIL>"
    SUBJECT = "Deviare Learner Activity"
    try:
        imap = IMAP4_SSL(host="outlook.office365.com", port=993)
        status, message = imap.login(
            user="<EMAIL>", password="Lam82139"
        )
        if status != "OK":
            raise Exception
        # We have been authed, select INBOX
        imap.select(mailbox="INBOX", readonly=True)

        date = datetime.now() - timedelta(days=7)
        # Partial match on subject header
        status, body = imap.search(
            None,
            '(FROM "%s")' % (MAIL_FROM),
            '(SUBJECT "%s")' % (SUBJECT),
            '(SINCE "%s")' % (date.strftime("%d-%b-%Y")),
        )
        if status != "OK":
            raise Exception

        # Assuming we only receive one email in time period
        identifier = body[0]

        status, body = imap.fetch(identifier, "(RFC822)")
        if status != "OK":
            raise Exception

        message = email.message_from_bytes(body[0][1])

        link = parse_email(message)
        imap.close()

        return link

    except Exception as exc:
        logger.exception(exc)
        return None


def to_secs(value):
    try:
        print(value)
        return pd.Timedelta(value).total_seconds() if str(value).find('None') == -1 and type(value) not in (
            None, False, np.infty, np.NaN, pd.NaT) else 0
    except ValueError as ve:
        return 0


def read_uri(url):
    import mimetypes
    from urllib.parse import urlparse
    from os.path import exists
    mime, n = mimetypes.guess_type(url)
    p = urlparse(url)
    if p.netloc == '':
        if exists(url):
            with open(url, 'rb') as f:
                data = f.read()
    else:
        r = requests.get(url)
        if r.ok:
            data = r.content
    return mime, data, p.path


def image_from(fn):
    mime, data, rn = read_uri(fn)
    bdata = base64.b64encode(data).decode('utf-8')
    if data:
        return f'data:{mime};base64,{bdata}'
    else:
        return fn


def get_courses_for_report(records,user=None):
    results = []
    for usr in records:
        userDict = {}
        if usr.user != None or user!=None:
            if user:
                userDict['Username'] = user.userName
                userDict['Learner Email'] = user.email
            else:
                userDict['Username'] = usr.user.userName
                userDict['Learner Email'] = usr.email
            try:
                userDict['account_status'] = usr.account_status
            except:
                userDict['account_status'] = "N/A"
            try:
                userDict['order_type'] = usr.order_type
            except:
                userDict['order_type'] = "N/A"
            try:
                userDict['team'] = [i['name'] for i in usr.team.all().values('name')]
            except:
                userDict['team'] = "N/A"
            try:
                userDict['course_assignment_date'] = usr.course_assignment_date
            except:
                userDict['course_assignment_date'] = "N/A"
            try:
                userDict['course_activation_date'] = usr.course_activation_date
            except:
                userDict['course_activation_date'] = "N/A"
            try:
                userDict["activity_level"] = usr.activity_level
            except:
                userDict["activity_level"] = usr.activity_level
            try:
                userDict["Cohort"] = [i['name'] for i in usr.team.all().values('name')]
            except:
                userDict["Cohort"] = "N/A"
            try:
                userDict["Assessment Test"] = usr.test_score
            except:
                userDict["Assessment Test"] = 0
            try:
                userDict["Project Status"] = (usr.project_result).lower()
            except:
                userDict["Project Status"] = "pending"
            userDict['course_type'] = usr.course_type
            try:
                userDict['course_id'] = usr.course_id
            except:
                userDict['course_id'] = "N/A"

            try:
                userDict['self_learning_completion'] = usr.self_learning_completion
            except:
                userDict['self_learning_completion'] = "N/A"
            try:
                userDict['course_expiration_date'] = usr.course_expiration_date
            except:
                userDict['course_expiration_date'] = "N/A"
            try:
                userDict['course_title'] = usr.course_title
            except:
                userDict['course_title'] = "N/A"
            try:
                userDict['test_score'] = usr.test_score
            except:
                userDict['test_score'] = "N/A"
            try:
                userDict['project_result'] = usr.project_result
            except:
                userDict['project_result'] = 'N/A'
            try:
                userDict['course_completion_date'] = usr.course_completion_date
            except:
                userDict['course_completion_date'] = "N/A"

            try:
                userDict['live_class_attended'] = usr.live_class_attended
            except:
                userDict['live_class_attended'] = "N/A"
            try:
                userDict['osl_score'] = usr.osl_score
            except:
                userDict['osl_score'] = "N/A"
            try:
                userDict['lvc_sore'] = usr.lvc_sore
            except:
                userDict['lvc_sore'] = "N/A"
            try:
                userDict['project_score'] = usr.project_score
            except:
                userDict['project_score'] = "N/A"

            try:
                userDict['certification_score'] = usr.certification_score
            except:
                userDict['certification_score'] = "N/A"

            try:
                userDict['concat'] = usr.concat
            except:
                userDict['concat'] = "N/A"
            try:
                userDict['program'] = usr.program
            except:
                userDict['program'] = "N/A"
            try:
                userDict['certification_status'] = usr.certification_status
            except:
                userDict['certification_status'] = "N/A"
            try:
                userDict['assessment'] = usr.assessment
            except:
                userDict['assessment'] = "N/A"

            try:
                userDict['last_login_date'] = usr.last_login_date
            except:
                userDict['last_login_date'] = "N/A"

            try:
                userDict['last_activity_on'] = usr.last_activity_on
            except:
                userDict['last_activity_on'] = "N/A"
            try:
                userDict['self_learning_time'] = usr.self_learning_time
            except:
                userDict['self_learning_time'] = "N/A"
            try:
                userDict['course_access'] = usr.course_access
            except:
                userDict['course_access'] = "N/A"
            try:
                userDict['program_id'] = usr.program_id
            except:
                userDict['program_id'] = "N/A"

            try:
                userDict['enrolment_cohort_id'] = usr.enrolment_cohort_id
            except:
                userDict['enrolment_cohort_id'] = "N/A"
            try:
                userDict['enrolment_cohort_name'] = usr.enrolment_cohort_name
            except:
                userDict['enrolment_cohort_name'] = "N/A"
            try:
                userDict['current_cohort_id'] = usr.current_cohort_id
            except:
                userDict['current_cohort_id'] = "N/A"
            try:
                userDict['current_cohort_name'] = usr.current_cohort_name
            except:
                userDict['current_cohort_name'] = "N/A"
            try:
                userDict['current_cohort_start_date'] = usr.current_cohort_start_date
            except:
                userDict['current_cohort_start_date'] = "N/A"

            try:
                userDict['current_cohort_end_date'] = usr.current_cohort_end_date
            except:
                userDict['current_cohort_end_date'] = "N/A"

            try:
                userDict['cohort_enrollment_date'] = usr.cohort_enrollment_date
            except:
                userDict['cohort_enrollment_date'] = "N/A"
            try:
                userDict['overall_classess'] = usr.overall_classess
            except:
                userDict['overall_classess'] = "N/A"
            try:
                userDict['mentoring_registered'] = usr.mentoring_registered
            except:
                userDict['mentoring_registered'] = "N/A"
            try:
                userDict['mentoring_attended'] = usr.mentoring_attended
            except:
                userDict['mentoring_attended'] = "N/A"

            try:
                userDict['live_classes_registered'] = usr.live_classes_registered
            except:
                userDict['live_classes_registered'] = "N/A"
            try:
                userDict['live_sessions_attended'] = usr.live_classes_registered
            except:
                userDict['live_sessions_attended'] = "N/A"
            results.append(userDict)
    return results


from main.models import (
    Composer, CourseLicense, CourseLicenseUser, AssessmentLicense, AssessmentLicenseUser,
    ApprenticeshipLicense, ApprenticeshipLicenseUser, UserSettings, ComposerLicense, ComposerLicenseUser
)

def assign_composer_to_user(composer_id, user_id):
    """
    Assign a composer to a user following the proper license validation pattern.
    This function now implements composer-level licensing instead of individual item licensing.
    """
    try:
        composer = Composer.objects.get(pk=composer_id)
        user = UserSettings.objects.get(pk=user_id)
        project = user.project_id

        if not project:
            return {
                "success": False,
                "message": f"User {user.userName} does not have a project assigned. Cannot assign licenses."
            }

        # Step 1: Check if ComposerLicense exists for this project and composer
        composer_license = ComposerLicense.objects.filter(
            project_id=project, composer_id=composer
        ).first()

        if not composer_license:
            return {
                "success": False,
                "message": f"No composer license found for {composer.product_name} in project {project.project_name}. Please allocate composer licenses to the project first."
            }

        # Step 2: Validate remaining licenses (following the same pattern as course/assessment/apprenticeship)
        user_assigned = ComposerLicenseUser.objects.filter(composer_license_id=composer_license).count()
        remaining_license = composer_license.initial_count - user_assigned

        if remaining_license <= 0:
            return {
                "success": False,
                "message": f"Cannot assign composer license. No remaining licenses available. Current usage: {user_assigned}/{composer_license.initial_count}"
            }

        # Step 3: Check if user is already assigned to this composer
        existing_assignment = ComposerLicenseUser.objects.filter(
            composer_license_id=composer_license, user_id=user
        ).first()

        if existing_assignment:
            return {
                "success": False,
                "message": f"User {user.userName} is already assigned to composer {composer.product_name}."
            }

        # Step 4: Create the composer license assignment
        ComposerLicenseUser.objects.create(
            composer_license_id=composer_license, user_id=user
        )

        # Step 5: Create individual item licenses for all items in the composer
        # This ensures the user can launch all items within the composer
        assigned = {"courses": [], "assessments": [], "apprenticeships": []}

        for course in composer.courses.all():
            # Handle potential duplicate CourseLicense records
            course_license = CourseLicense.objects.filter(
                project_id=project, course_id=course
            ).first()

            if not course_license:
                course_license = CourseLicense.objects.create(
                    project_id=project, course_id=course
                )

            clu, created = CourseLicenseUser.objects.get_or_create(
                course_license_id=course_license, user_id=user
            )
            if created:
                assigned["courses"].append(course.name)

        for assessment in composer.assessments.all():
            # Handle potential duplicate AssessmentLicense records
            assessment_license = AssessmentLicense.objects.filter(
                project_id=project, assessment_id=assessment
            ).first()

            if not assessment_license:
                assessment_license = AssessmentLicense.objects.create(
                    project_id=project, assessment_id=assessment
                )

            _, created = AssessmentLicenseUser.objects.get_or_create(
                assessment_license_id=assessment_license, user_id=user
            )
            if created:
                assigned["assessments"].append(assessment.name)

        for apprenticeship in composer.apprenticeships.all():
            # Handle potential duplicate ApprenticeshipLicense records
            apprenticeship_license = ApprenticeshipLicense.objects.filter(
                project=project, apprenticeship=apprenticeship
            ).first()

            if not apprenticeship_license:
                apprenticeship_license = ApprenticeshipLicense.objects.create(
                    project=project, apprenticeship=apprenticeship
                )

            _, created = ApprenticeshipLicenseUser.objects.get_or_create(
                apprenticeship_license=apprenticeship_license, user=user
            )
            if created:
                assigned["apprenticeships"].append(apprenticeship.name)

        return {
            "success": True,
            "assigned": assigned,
            "remaining_licenses": remaining_license - 1,  # Subtract 1 for the license we just used
            "message": f"Successfully assigned composer {composer.product_name} to user {user.userName}. Remaining licenses: {remaining_license - 1}"
        }
    except Composer.DoesNotExist:
        return {"success": False, "message": "Composer not found."}
    except UserSettings.DoesNotExist:
        return {"success": False, "message": "User not found."}
    except Exception as e:
        return {"success": False, "message": str(e)}
    

