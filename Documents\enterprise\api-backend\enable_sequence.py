#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import Composer

# Enable sequence for composer 1
try:
    c = Composer.objects.get(id=1)
    print(f'Before: Composer "{c.product_name}" is_sequence: {c.is_sequence}')
    
    c.is_sequence = True
    c.save()
    
    print(f'After: Composer "{c.product_name}" is_sequence: {c.is_sequence}')
    print('Sequence enabled successfully!')
        
except Exception as e:
    print(f'Error: {e}')
