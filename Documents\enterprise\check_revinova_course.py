#!/usr/bin/env python
"""
Check if the enrolled Revinova course is in any composer
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, 'api-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deviare.settings')
django.setup()

from main.models import Course, CourseLicenseUser, UserSettings, Composer

def check_revinova_course():
    print("=== CHECKING ENROLLED REVINOVA COURSE ===")
    
    # Get the enrolled Revinova course
    enrolled_revinova_uuid = "00284ba3-26e8-4a2a-9d2a-5d1991c4554e"
    
    try:
        course = Course.objects.get(uuid=enrolled_revinova_uuid)
        print(f"Course: {course.name}")
        print(f"Provider: {course.provider}")
        print(f"Link: {course.link}")
        print(f"UUID: {course.uuid}")
        
        # Check if this course is in any composer
        composers_with_course = Composer.objects.filter(courses=course)
        print(f"\nComposers containing this course: {composers_with_course.count()}")
        
        for composer in composers_with_course:
            print(f"  - {composer.product_name} (ID: {composer.id})")
        
        if composers_with_course.count() == 0:
            print("❌ This course is NOT in any composer!")
            print("This is why it doesn't appear in the API response.")
            
            # Check if we should add it to the existing composer
            existing_composer = Composer.objects.get(id=1)  # "My Bundle"
            print(f"\nShould we add this course to '{existing_composer.product_name}'?")
            print("This would make it appear in the API response.")
        
    except Course.DoesNotExist:
        print(f"Course {enrolled_revinova_uuid} not found")

if __name__ == "__main__":
    check_revinova_course()
